"use client"

import { useEffect } from 'react'

// Web Vitals tracking for performance monitoring
export function WebVitals() {
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Track Core Web Vitals
      import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
        getCLS(console.log)
        getFID(console.log)
        getFCP(console.log)
        getLCP(console.log)
        getTTFB(console.log)
      }).catch(() => {
        // Fallback if web-vitals is not available
        console.log('Web Vitals not available')
      })
    }
  }, [])

  return null
}

// Performance observer for monitoring
export function PerformanceMonitor() {
  useEffect(() => {
    if (typeof window !== 'undefined' && 'PerformanceObserver' in window) {
      // Monitor Long Tasks
      try {
        const longTaskObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.duration > 50) {
              console.warn('Long task detected:', entry)
            }
          }
        })
        longTaskObserver.observe({ entryTypes: ['longtask'] })

        // Monitor Layout Shifts
        const layoutShiftObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (!entry.hadRecentInput) {
              console.log('Layout shift:', entry)
            }
          }
        })
        layoutShiftObserver.observe({ entryTypes: ['layout-shift'] })

        return () => {
          longTaskObserver.disconnect()
          layoutShiftObserver.disconnect()
        }
      } catch (error) {
        console.log('Performance monitoring not supported')
      }
    }
  }, [])

  return null
}

// Image lazy loading utility
export function useImageLazyLoading() {
  useEffect(() => {
    if ('IntersectionObserver' in window) {
      const imageObserver = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement
            if (img.dataset.src) {
              img.src = img.dataset.src
              img.classList.add('loaded')
              imageObserver.unobserve(img)
            }
          }
        })
      })

      const images = document.querySelectorAll('img[data-src]')
      images.forEach((img) => imageObserver.observe(img))

      return () => imageObserver.disconnect()
    }
  }, [])
}

// Preload critical resources
export function PreloadResources() {
  useEffect(() => {
    // Preload critical fonts
    const fontPreloads = [
      'https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap',
      'https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@100;200;300;400;500;600;700;800&display=swap'
    ]

    fontPreloads.forEach((href) => {
      const link = document.createElement('link')
      link.rel = 'preload'
      link.as = 'style'
      link.href = href
      document.head.appendChild(link)
    })

    // Preload critical images (if any)
    const criticalImages = [
      // Add your critical image URLs here
    ]

    criticalImages.forEach((src) => {
      const link = document.createElement('link')
      link.rel = 'preload'
      link.as = 'image'
      link.href = src
      document.head.appendChild(link)
    })
  }, [])

  return null
}

// Service Worker registration for caching
export function ServiceWorkerRegistration() {
  useEffect(() => {
    if (
      typeof window !== 'undefined' &&
      'serviceWorker' in navigator &&
      process.env.NODE_ENV === 'production'
    ) {
      navigator.serviceWorker
        .register('/sw.js')
        .then((registration) => {
          console.log('SW registered: ', registration)
        })
        .catch((registrationError) => {
          console.log('SW registration failed: ', registrationError)
        })
    }
  }, [])

  return null
}

// Bundle analyzer helper (development only)
export function BundleAnalyzer() {
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      // Log bundle information in development
      console.log('Bundle analysis available at: /_next/static/chunks/')
    }
  }, [])

  return null
}
