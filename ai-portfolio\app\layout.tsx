import type { <PERSON>ada<PERSON> } from "next";
import { Inter, JetBrains_Mono } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/components/theme-provider";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

const jetbrainsMono = JetBrains_Mono({
  subsets: ["latin"],
  variable: "--font-jetbrains-mono",
});

export const metadata: Metadata = {
  title: "AI Portfolio | Artificial Intelligence & Data Science Graduate",
  description: "Portfolio of an AI & Data Science graduate (Class of 2025) showcasing expertise in Python, Machine Learning, Full Stack Development, and cutting-edge AI projects.",
  keywords: ["AI", "Data Science", "Machine Learning", "Python", "Portfolio", "Full Stack", "React", "Next.js"],
  authors: [{ name: "AI & Data Science Graduate" }],
  creator: "AI & Data Science Graduate",
  openGraph: {
    type: "website",
    locale: "en_US",
    title: "AI Portfolio | Artificial Intelligence & Data Science Graduate",
    description: "Explore cutting-edge AI projects and innovative solutions from a passionate AI & Data Science graduate.",
    siteName: "AI Portfolio",
  },
  twitter: {
    card: "summary_large_image",
    title: "AI Portfolio | Artificial Intelligence & Data Science Graduate",
    description: "Explore cutting-edge AI projects and innovative solutions from a passionate AI & Data Science graduate.",
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${inter.variable} ${jetbrainsMono.variable} antialiased`}
      >
        <ThemeProvider defaultTheme="dark">
          {children}
        </ThemeProvider>
      </body>
    </html>
  );
}
