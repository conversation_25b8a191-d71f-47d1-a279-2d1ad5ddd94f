"use client"

import { useRef } from 'react'
import { motion, useInView } from 'framer-motion'
import { Briefcase, Code, GraduationCap, Award } from 'lucide-react'
import { Timeline } from '@/components/ui/timeline'
import { Card, CardContent } from '@/components/ui/card'

const experienceData = [
  {
    title: "Full Stack Web Developer Intern",
    company: "CAUSEVE TECHNOLOGIES LLP",
    location: "On-site",
    duration: "2024",
    type: "internship" as const,
    description: "Completed a comprehensive full stack web development internship specializing in Java backend and modern frontend technologies. Built enterprise-level web applications using HTML, CSS, JavaScript, Bootstrap for frontend and Java with MySQL for backend operations.",
    achievements: [
      "Developed full stack web applications using Java backend and HTML/CSS/JavaScript frontend",
      "Built responsive user interfaces using Bootstrap and modern CSS techniques",
      "Implemented RESTful APIs and backend services using Java and Spring Boot",
      "Designed and managed MySQL databases with optimized queries",
      "Created CRUD operations and real-time web application features",
      "Gained expertise in full stack development lifecycle"
    ],
    technologies: ["Java", "Spring Boot", "HTML5", "CSS3", "JavaScript", "Bootstrap", "MySQL", "REST APIs"],
    link: "#"
  },
  {
    title: "Backend Java Developer Intern",
    company: "CYBERNAUT EDUTECH PVT LTD",
    location: "Remote",
    duration: "2024",
    type: "internship" as const,
    description: "Specialized in Java backend development and automation systems. Developed enterprise-grade applications including an automated price monitoring system with email notifications, demonstrating expertise in Java programming and system integration.",
    achievements: [
      "Built automated price monitoring system using Java and web scraping",
      "Implemented email notification system using JavaMail API",
      "Created scheduled tasks and background services using Java",
      "Developed REST APIs for system integration",
      "Optimized application performance and database queries",
      "Delivered production-ready Java applications"
    ],
    technologies: ["Java", "Spring Boot", "JavaMail API", "REST APIs", "MySQL", "Task Scheduling", "Web Scraping"],
    link: "https://cybernaut.co.in/"
  },
  {
    title: "Healthcare Management System",
    company: "College Project",
    location: "Mahendra Engineering College",
    duration: "2024",
    type: "project" as const,
    description: "Developed a comprehensive healthcare management web application using Java Spring Boot backend and Bootstrap frontend. The system manages patient records, appointments, and medical history with secure authentication and responsive design.",
    achievements: [
      "Built full stack web application using Java Spring Boot and Bootstrap",
      "Implemented secure user authentication and authorization",
      "Created responsive frontend with HTML5, CSS3, and JavaScript",
      "Designed MySQL database with optimized schema",
      "Developed RESTful APIs for frontend-backend communication",
      "Implemented CRUD operations for patient and appointment management"
    ],
    technologies: ["Java", "Spring Boot", "HTML5", "CSS3", "JavaScript", "Bootstrap", "MySQL", "REST APIs"],
    link: "#"
  },
  {
    title: "E-Commerce Web Application",
    company: "College Project",
    location: "Mahendra Engineering College",
    duration: "2024",
    type: "project" as const,
    description: "Built a complete e-commerce web application using Java Spring Boot backend and responsive Bootstrap frontend. Features include product catalog, shopping cart, user authentication, order management, and payment integration with modern UI/UX design.",
    achievements: [
      "Developed full stack e-commerce platform using Java and Bootstrap",
      "Implemented shopping cart and order management system",
      "Created responsive product catalog with search and filtering",
      "Built secure user authentication and session management",
      "Integrated payment gateway and order tracking",
      "Designed modern UI with Bootstrap and custom CSS"
    ],
    technologies: ["Java", "Spring Boot", "HTML5", "CSS3", "JavaScript", "Bootstrap", "MySQL", "Payment Gateway"],
    link: "#"
  },
  {
    title: "Student Management System",
    company: "College Project",
    location: "Mahendra Engineering College",
    duration: "2024",
    type: "project" as const,
    description: "Developed a comprehensive student management web application using Java Spring Boot and Bootstrap. The system manages student records, course enrollment, grades, attendance, and generates reports with role-based access control for students, teachers, and administrators.",
    achievements: [
      "Built complete student management system using Java full stack",
      "Implemented role-based access control (Student, Teacher, Admin)",
      "Created responsive dashboard with Bootstrap and JavaScript",
      "Developed grade management and attendance tracking",
      "Built report generation system with PDF export",
      "Implemented real-time notifications and messaging system"
    ],
    technologies: ["Java", "Spring Boot", "HTML5", "CSS3", "JavaScript", "Bootstrap", "MySQL", "JPA"],
    link: "#"
  },
  {
    title: "B.Tech in Artificial Intelligence & Data Science",
    company: "Mahendra Engineering College",
    location: "Tamil Nadu, India",
    duration: "2021 - 2025",
    type: "education" as const,
    description: "Comprehensive program with specialization in Full Stack Development using Java backend and modern frontend technologies. Strong focus on enterprise application development, web technologies, and software engineering principles.",
    achievements: [
      "Specialized in Java Full Stack Development and web technologies",
      "Completed multiple enterprise-level web application projects",
      "Gained expertise in Java, Spring Boot, HTML, CSS, JavaScript, Bootstrap",
      "Developed practical skills in database design and REST API development",
      "Strong foundation in software engineering and system design"
    ],
    technologies: ["Java", "Spring Boot", "HTML5", "CSS3", "JavaScript", "Bootstrap", "MySQL", "REST APIs"],
    link: "#"
  },
  {
    title: "Higher Secondary Education",
    company: "Sathya Saai Matric Higher Secondary School",
    location: "Tamil Nadu, India",
    duration: "2020 - 2021",
    type: "education" as const,
    description: "Higher Secondary Certificate (HSC) with strong foundation in mathematics, science, and computer applications, preparing for engineering studies.",
    achievements: [
      "Completed HSC with excellent academic performance",
      "Strong foundation in mathematics and science",
      "Developed interest in computer science and technology",
      "Prepared for engineering entrance examinations"
    ],
    technologies: ["Mathematics", "Physics", "Chemistry", "Computer Science"],
    link: "#"
  }
]

const stats = [
  { label: "Internships Completed", value: "2", icon: Briefcase },
  { label: "Projects Built", value: "15+", icon: Code },
  { label: "Certifications", value: "6", icon: Award },
  { label: "Years of Learning", value: "4", icon: GraduationCap }
]

export default function Experience() {
  const ref = useRef<HTMLElement>(null)
  const isInView = useInView(ref, { once: true, margin: "-100px" })

  return (
    <section ref={ref} className="py-20 bg-background relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `
            linear-gradient(45deg, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
            linear-gradient(-45deg, rgba(139, 92, 246, 0.1) 0%, transparent 50%)
          `
        }} />
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Section Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.6 }}
        >
          <h2 className="text-4xl md:text-5xl font-bold gradient-text mb-6">
            Experience & Journey
          </h2>
          <p className="text-xl text-foreground/80 max-w-3xl mx-auto leading-relaxed">
            My professional journey through internships, projects, and continuous learning in Full Stack development.
            Each experience has shaped my expertise in Java backend development, frontend technologies, and enterprise web applications.
          </p>
        </motion.div>

        {/* Stats Grid */}
        <motion.div
          className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16"
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          {stats.map(({ label, value, icon: Icon }, index) => (
            <motion.div
              key={label}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.8 }}
              transition={{ duration: 0.4, delay: (index * 100) / 1000 }}
            >
              <Card variant="glass" className="text-center hover:glow-blue transition-all duration-300">
                <CardContent className="p-6">
                  <Icon className="w-8 h-8 text-primary mx-auto mb-3" />
                  <div className="text-3xl font-bold gradient-text mb-1">{value}</div>
                  <div className="text-sm text-foreground/70">{label}</div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Timeline */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <Timeline items={experienceData} />
        </motion.div>

        {/* Call to Action */}
        <motion.div
          className="text-center mt-16"
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.6, delay: 0.6 }}
        >
          <Card variant="gradient" className="max-w-2xl mx-auto">
            <CardContent className="p-8 text-center">
              <h3 className="text-2xl font-bold text-foreground mb-4">
                Ready for New Challenges
              </h3>
              <p className="text-foreground/80 mb-6">
                I'm actively seeking opportunities to apply my skills in Java backend development,
                frontend technologies, and full-stack web development to solve real-world problems and create innovative solutions.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <motion.a
                  href="#contact"
                  className="px-6 py-3 bg-gradient-to-r from-cyber-blue to-hologram-blue text-white rounded-lg font-medium hover:shadow-lg transition-all duration-300"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Get In Touch
                </motion.a>
                <motion.a
                  href="#projects"
                  className="px-6 py-3 border border-primary/50 text-primary rounded-lg font-medium hover:bg-primary/10 transition-all duration-300"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  View Projects
                </motion.a>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  )
}
