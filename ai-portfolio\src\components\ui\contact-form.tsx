"use client"

import { useState, useRef } from 'react'
import { motion, useInView } from 'framer-motion'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Send, User, Mail, MessageSquare, CheckCircle, AlertCircle, Loader2 } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { cn } from '@/lib/utils'

const contactSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  subject: z.string().min(5, 'Subject must be at least 5 characters'),
  message: z.string().min(10, 'Message must be at least 10 characters')
})

type ContactFormData = z.infer<typeof contactSchema>

interface ContactFormProps {
  className?: string
}

export function ContactForm({ className }: ContactFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle')
  const ref = useRef<HTMLDivElement>(null)
  const isInView = useInView(ref, { once: true, margin: "-100px" })

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch
  } = useForm<ContactFormData>({
    resolver: zodResolver(contactSchema)
  })

  const watchedFields = watch()

  const onSubmit = async (data: ContactFormData) => {
    setIsSubmitting(true)
    setSubmitStatus('idle')

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Here you would typically send the data to your backend
      console.log('Form submitted:', data)
      
      setSubmitStatus('success')
      reset()
    } catch (error) {
      setSubmitStatus('error')
    } finally {
      setIsSubmitting(false)
    }
  }

  const inputVariants = {
    focus: { scale: 1.02, transition: { duration: 0.2 } },
    blur: { scale: 1, transition: { duration: 0.2 } }
  }

  return (
    <motion.div
      ref={ref}
      className={cn("max-w-2xl mx-auto", className)}
      initial={{ opacity: 0, y: 30 }}
      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
      transition={{ duration: 0.6 }}
    >
      <Card variant="neural" className="overflow-hidden">
        <CardContent className="p-8">
          {/* Header */}
          <div className="text-center mb-8">
            <motion.div
              className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-cyber-blue/20 to-electric-violet/20 flex items-center justify-center"
              animate={{ rotate: 360 }}
              transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
            >
              <MessageSquare className="w-8 h-8 text-primary" />
            </motion.div>
            <h3 className="text-2xl font-bold text-foreground mb-2">Let's Connect</h3>
            <p className="text-foreground/70">
              Ready to discuss your next project or just want to say hello? 
              I'd love to hear from you!
            </p>
          </div>

          {/* Success/Error Messages */}
          {submitStatus === 'success' && (
            <motion.div
              className="mb-6 p-4 rounded-lg bg-neon-green/10 border border-neon-green/30 flex items-center space-x-3"
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              <CheckCircle className="w-5 h-5 text-neon-green" />
              <span className="text-neon-green font-medium">
                Message sent successfully! I'll get back to you soon.
              </span>
            </motion.div>
          )}

          {submitStatus === 'error' && (
            <motion.div
              className="mb-6 p-4 rounded-lg bg-red-500/10 border border-red-500/30 flex items-center space-x-3"
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              <AlertCircle className="w-5 h-5 text-red-500" />
              <span className="text-red-500 font-medium">
                Something went wrong. Please try again later.
              </span>
            </motion.div>
          )}

          {/* Form */}
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* Name Field */}
            <motion.div
              className="space-y-2"
              variants={inputVariants}
              whileFocus="focus"
              initial="blur"
            >
              <label className="block text-sm font-medium text-foreground">
                Name *
              </label>
              <div className="relative">
                <User className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-foreground/50" />
                <input
                  {...register('name')}
                  type="text"
                  placeholder="Your full name"
                  className={cn(
                    "w-full pl-12 pr-4 py-3 rounded-lg border bg-background/50 backdrop-blur-sm",
                    "text-foreground placeholder:text-foreground/50",
                    "focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary/50",
                    "transition-all duration-300",
                    errors.name ? "border-red-500/50" : "border-border/50"
                  )}
                />
                {watchedFields.name && !errors.name && (
                  <CheckCircle className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-neon-green" />
                )}
              </div>
              {errors.name && (
                <motion.p
                  className="text-sm text-red-500"
                  initial={{ opacity: 0, y: -5 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.2 }}
                >
                  {errors.name.message}
                </motion.p>
              )}
            </motion.div>

            {/* Email Field */}
            <motion.div
              className="space-y-2"
              variants={inputVariants}
              whileFocus="focus"
              initial="blur"
            >
              <label className="block text-sm font-medium text-foreground">
                Email *
              </label>
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-foreground/50" />
                <input
                  {...register('email')}
                  type="email"
                  placeholder="<EMAIL>"
                  className={cn(
                    "w-full pl-12 pr-4 py-3 rounded-lg border bg-background/50 backdrop-blur-sm",
                    "text-foreground placeholder:text-foreground/50",
                    "focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary/50",
                    "transition-all duration-300",
                    errors.email ? "border-red-500/50" : "border-border/50"
                  )}
                />
                {watchedFields.email && !errors.email && (
                  <CheckCircle className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-neon-green" />
                )}
              </div>
              {errors.email && (
                <motion.p
                  className="text-sm text-red-500"
                  initial={{ opacity: 0, y: -5 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.2 }}
                >
                  {errors.email.message}
                </motion.p>
              )}
            </motion.div>

            {/* Subject Field */}
            <motion.div
              className="space-y-2"
              variants={inputVariants}
              whileFocus="focus"
              initial="blur"
            >
              <label className="block text-sm font-medium text-foreground">
                Subject *
              </label>
              <div className="relative">
                <input
                  {...register('subject')}
                  type="text"
                  placeholder="What's this about?"
                  className={cn(
                    "w-full px-4 py-3 rounded-lg border bg-background/50 backdrop-blur-sm",
                    "text-foreground placeholder:text-foreground/50",
                    "focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary/50",
                    "transition-all duration-300",
                    errors.subject ? "border-red-500/50" : "border-border/50"
                  )}
                />
                {watchedFields.subject && !errors.subject && (
                  <CheckCircle className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-neon-green" />
                )}
              </div>
              {errors.subject && (
                <motion.p
                  className="text-sm text-red-500"
                  initial={{ opacity: 0, y: -5 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.2 }}
                >
                  {errors.subject.message}
                </motion.p>
              )}
            </motion.div>

            {/* Message Field */}
            <motion.div
              className="space-y-2"
              variants={inputVariants}
              whileFocus="focus"
              initial="blur"
            >
              <label className="block text-sm font-medium text-foreground">
                Message *
              </label>
              <div className="relative">
                <textarea
                  {...register('message')}
                  rows={5}
                  placeholder="Tell me about your project, ideas, or just say hello..."
                  className={cn(
                    "w-full px-4 py-3 rounded-lg border bg-background/50 backdrop-blur-sm resize-none",
                    "text-foreground placeholder:text-foreground/50",
                    "focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary/50",
                    "transition-all duration-300",
                    errors.message ? "border-red-500/50" : "border-border/50"
                  )}
                />
                {watchedFields.message && !errors.message && (
                  <CheckCircle className="absolute right-3 top-3 w-5 h-5 text-neon-green" />
                )}
              </div>
              {errors.message && (
                <motion.p
                  className="text-sm text-red-500"
                  initial={{ opacity: 0, y: -5 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.2 }}
                >
                  {errors.message.message}
                </motion.p>
              )}
            </motion.div>

            {/* Submit Button */}
            <motion.div
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Button
                type="submit"
                size="lg"
                variant="primary"
                disabled={isSubmitting}
                className="w-full group relative overflow-hidden"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                    Sending Message...
                  </>
                ) : (
                  <>
                    <Send className="w-5 h-5 mr-2 group-hover:translate-x-1 transition-transform" />
                    Send Message
                  </>
                )}
                
                {/* Button animation */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700" />
              </Button>
            </motion.div>
          </form>

          {/* Footer */}
          <div className="mt-8 text-center text-sm text-foreground/60">
            <p>
              I typically respond within 24 hours. Looking forward to connecting with you!
            </p>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}
