"use client"

import { motion } from 'framer-motion'
import { cn } from '@/lib/utils'

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

export function LoadingSpinner({ size = 'md', className }: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12'
  }

  return (
    <motion.div
      className={cn(
        "inline-block border-2 border-primary/30 border-t-primary rounded-full",
        sizeClasses[size],
        className
      )}
      animate={{ rotate: 360 }}
      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
    />
  )
}

export function NeuralLoadingAnimation() {
  return (
    <div className="flex items-center justify-center min-h-screen bg-background">
      <div className="relative">
        {/* Central Node */}
        <motion.div
          className="w-4 h-4 bg-cyber-blue rounded-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
          animate={{
            scale: [1, 1.5, 1],
            opacity: [0.5, 1, 0.5]
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />

        {/* Orbiting Nodes */}
        {[0, 1, 2, 3, 4, 5].map((index) => (
          <motion.div
            key={index}
            className="w-2 h-2 bg-electric-violet rounded-full absolute"
            style={{
              top: '50%',
              left: '50%',
              transformOrigin: '0 0'
            }}
            animate={{
              rotate: 360,
              scale: [0.5, 1, 0.5]
            }}
            transition={{
              rotate: {
                duration: 3,
                repeat: Infinity,
                ease: "linear",
                delay: index * 0.5
              },
              scale: {
                duration: 1.5,
                repeat: Infinity,
                ease: "easeInOut",
                delay: index * 0.25
              }
            }}
            initial={{
              x: Math.cos((index * 60) * Math.PI / 180) * 40,
              y: Math.sin((index * 60) * Math.PI / 180) * 40
            }}
          />
        ))}

        {/* Loading Text */}
        <motion.div
          className="absolute top-full mt-8 left-1/2 transform -translate-x-1/2"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
        >
          <div className="text-center">
            <h3 className="text-xl font-bold gradient-text mb-2">
              Initializing AI Portfolio
            </h3>
            <div className="flex items-center justify-center space-x-1">
              {[0, 1, 2].map((index) => (
                <motion.div
                  key={index}
                  className="w-2 h-2 bg-primary rounded-full"
                  animate={{
                    y: [0, -10, 0],
                    opacity: [0.5, 1, 0.5]
                  }}
                  transition={{
                    duration: 1,
                    repeat: Infinity,
                    delay: index * 0.2
                  }}
                />
              ))}
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  )
}

interface LazyLoadWrapperProps {
  children: React.ReactNode
  fallback?: React.ReactNode
  className?: string
}

export function LazyLoadWrapper({ 
  children, 
  fallback = <LoadingSpinner />, 
  className 
}: LazyLoadWrapperProps) {
  return (
    <div className={cn("min-h-[200px] flex items-center justify-center", className)}>
      <React.Suspense fallback={fallback}>
        {children}
      </React.Suspense>
    </div>
  )
}

// Skeleton loading components
export function SkeletonCard() {
  return (
    <div className="glass rounded-xl p-6 animate-pulse">
      <div className="h-4 bg-foreground/10 rounded mb-4" />
      <div className="h-3 bg-foreground/10 rounded mb-2" />
      <div className="h-3 bg-foreground/10 rounded mb-4 w-3/4" />
      <div className="flex space-x-2">
        <div className="h-6 bg-foreground/10 rounded w-16" />
        <div className="h-6 bg-foreground/10 rounded w-20" />
        <div className="h-6 bg-foreground/10 rounded w-14" />
      </div>
    </div>
  )
}

export function SkeletonProject() {
  return (
    <div className="glass rounded-xl overflow-hidden animate-pulse">
      <div className="aspect-video bg-foreground/10" />
      <div className="p-6">
        <div className="h-5 bg-foreground/10 rounded mb-3" />
        <div className="h-3 bg-foreground/10 rounded mb-2" />
        <div className="h-3 bg-foreground/10 rounded mb-4 w-2/3" />
        <div className="flex space-x-2 mb-4">
          <div className="h-5 bg-foreground/10 rounded w-12" />
          <div className="h-5 bg-foreground/10 rounded w-16" />
          <div className="h-5 bg-foreground/10 rounded w-14" />
        </div>
        <div className="h-8 bg-foreground/10 rounded" />
      </div>
    </div>
  )
}

import React from 'react'
