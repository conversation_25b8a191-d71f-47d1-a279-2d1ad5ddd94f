#!/bin/bash

# AI Portfolio Deployment Script
# This script helps deploy the portfolio to various platforms

echo "🚀 AI Portfolio Deployment Script"
echo "=================================="

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

echo "✅ Node.js and npm are installed"

# Install dependencies
echo "📦 Installing dependencies..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies"
    exit 1
fi

echo "✅ Dependencies installed successfully"

# Run linting
echo "🔍 Running linting..."
npm run lint

if [ $? -ne 0 ]; then
    echo "⚠️  Linting issues found. Please fix them before deploying."
    read -p "Do you want to continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Build the project
echo "🏗️  Building the project..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ Build failed. Please fix the errors and try again."
    exit 1
fi

echo "✅ Build completed successfully"

# Ask for deployment platform
echo ""
echo "🌐 Choose deployment platform:"
echo "1) Vercel (Recommended)"
echo "2) Netlify"
echo "3) GitHub Pages"
echo "4) Custom Server"
echo "5) Local Preview"

read -p "Enter your choice (1-5): " choice

case $choice in
    1)
        echo "🚀 Deploying to Vercel..."
        if ! command -v vercel &> /dev/null; then
            echo "Installing Vercel CLI..."
            npm install -g vercel
        fi
        echo "🔐 Please login to Vercel when prompted..."
        vercel login
        echo "🚀 Deploying to production..."
        vercel --prod
        echo ""
        echo "🎉 Deployment complete!"
        echo "Your portfolio is now live!"
        ;;
    2)
        echo "🚀 Deploying to Netlify..."
        if ! command -v netlify &> /dev/null; then
            echo "Installing Netlify CLI..."
            npm install -g netlify-cli
        fi
        echo "🔐 Please login to Netlify when prompted..."
        netlify login
        echo "🚀 Deploying to production..."
        netlify deploy --prod --dir=out
        ;;
    3)
        echo "🚀 Preparing for GitHub Pages..."
        echo ""
        echo "📋 GitHub Pages Setup Instructions:"
        echo "1. Push your code to GitHub repository"
        echo "2. Go to repository Settings → Pages"
        echo "3. Select 'Deploy from a branch'"
        echo "4. Choose 'gh-pages' branch"
        echo ""
        echo "🔧 Or use GitHub Actions (advanced):"
        echo "The .github/workflows/deploy.yml file is already configured"
        echo "Just push to main branch and it will auto-deploy"
        ;;
    4)
        echo "🚀 Custom Server Deployment"
        echo ""
        echo "📁 Build files are ready in .next directory"
        echo "📋 Server deployment steps:"
        echo "1. Upload entire project to your server"
        echo "2. Install Node.js on server"
        echo "3. Run: npm install --production"
        echo "4. Run: npm start"
        echo "5. Configure reverse proxy (nginx/apache)"
        ;;
    5)
        echo "🚀 Starting local preview..."
        echo "Opening http://localhost:3000"
        npm start &
        sleep 3
        if command -v start &> /dev/null; then
            start http://localhost:3000
        elif command -v open &> /dev/null; then
            open http://localhost:3000
        elif command -v xdg-open &> /dev/null; then
            xdg-open http://localhost:3000
        fi
        ;;
    *)
        echo "❌ Invalid choice. Exiting."
        exit 1
        ;;
esac

echo ""
echo "🎉 Deployment process completed!"
echo ""
echo "📋 Post-deployment checklist:"
echo "✓ Test the website on different devices"
echo "✓ Check all links and forms"
echo "✓ Verify 3D animations work properly"
echo "✓ Test contact form functionality"
echo "✓ Check performance with Lighthouse"
echo "✓ Update social media links"
echo "✓ Add Google Analytics (if needed)"
echo ""
echo "🌟 Your AI Portfolio is ready to impress!"
