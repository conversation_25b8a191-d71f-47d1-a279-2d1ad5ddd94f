(()=>{var e={};e.id=492,e.ids=[492],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1510:(e,t,r)=>{Promise.resolve().then(r.bind(r,6871))},1758:(e,t,r)=>{Promise.resolve().then(r.bind(r,3701))},2704:()=>{},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3701:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>n});var o=r(2907);(0,o.registerClientReference)(function(){throw Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\PORTFILO\\ai-portfolio\\src\\components\\theme-provider.tsx","useTheme");let n=(0,o.registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\PORTFILO\\ai-portfolio\\src\\components\\theme-provider.tsx","ThemeProvider");(0,o.registerClientReference)(function(){throw Error("Attempted to call ThemeToggle() from the server but ThemeToggle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\PORTFILO\\ai-portfolio\\src\\components\\theme-provider.tsx","ThemeToggle")},3873:e=>{"use strict";e.exports=require("path")},4883:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},6871:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>s,U:()=>a});var o=r(687),n=r(3210);let i=(0,n.createContext)(void 0);function s({children:e,defaultTheme:t="dark"}){let[r,s]=(0,n.useState)(t),[a,l]=(0,n.useState)(!1);return a?(0,o.jsx)(i.Provider,{value:{theme:r,toggleTheme:()=>{s(e=>"dark"===e?"light":"dark")},setTheme:e=>{s(e)}},children:e}):(0,o.jsx)("div",{style:{visibility:"hidden"},children:e})}function a(){let{theme:e,toggleTheme:t}=function(){let e=(0,n.useContext)(i);if(void 0===e)throw Error("useTheme must be used within a ThemeProvider");return e}();return(0,o.jsxs)("button",{onClick:t,className:"relative p-2 rounded-lg glass hover:glow-blue transition-all duration-300 group","aria-label":"Toggle theme",children:[(0,o.jsxs)("div",{className:"relative w-6 h-6",children:[(0,o.jsx)("svg",{className:`absolute inset-0 w-6 h-6 transition-all duration-300 ${"light"===e?"rotate-0 scale-100 opacity-100":"rotate-90 scale-0 opacity-0"}`,fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"})}),(0,o.jsx)("svg",{className:`absolute inset-0 w-6 h-6 transition-all duration-300 ${"dark"===e?"rotate-0 scale-100 opacity-100":"-rotate-90 scale-0 opacity-0"}`,fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"})})]}),(0,o.jsx)("div",{className:"absolute inset-0 rounded-lg bg-gradient-to-r from-cyber-blue/20 to-electric-violet/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"})]})}},8014:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c,metadata:()=>d});var o=r(7413),n=r(1455),i=r.n(n),s=r(8134),a=r.n(s);r(2704);var l=r(3701);let d={title:"AI Portfolio | Artificial Intelligence & Data Science Graduate",description:"Portfolio of an AI & Data Science graduate (Class of 2025) showcasing expertise in Python, Machine Learning, Full Stack Development, and cutting-edge AI projects.",keywords:["AI","Data Science","Machine Learning","Python","Portfolio","Full Stack","React","Next.js"],authors:[{name:"AI & Data Science Graduate"}],creator:"AI & Data Science Graduate",openGraph:{type:"website",locale:"en_US",title:"AI Portfolio | Artificial Intelligence & Data Science Graduate",description:"Explore cutting-edge AI projects and innovative solutions from a passionate AI & Data Science graduate.",siteName:"AI Portfolio"},twitter:{card:"summary_large_image",title:"AI Portfolio | Artificial Intelligence & Data Science Graduate",description:"Explore cutting-edge AI projects and innovative solutions from a passionate AI & Data Science graduate."},robots:{index:!0,follow:!0}};function c({children:e}){return(0,o.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,o.jsx)("body",{className:`${i().variable} ${a().variable} antialiased`,children:(0,o.jsx)(l.ThemeProvider,{defaultTheme:"dark",children:e})})})}},8443:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},8536:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var o=r(5239),n=r(8088),i=r(8170),s=r.n(i),a=r(893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let d={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,8014)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\PORTFILO\\ai-portfolio\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=[],u={require:r,loadChunk:()=>Promise.resolve()},p=new o.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[447,418],()=>r(8536));module.exports=o})();