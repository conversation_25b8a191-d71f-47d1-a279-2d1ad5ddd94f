"use client"

import { useState, useRef, useMemo } from 'react'
import { motion, useInView, AnimatePresence } from 'framer-motion'
import { ProjectCard } from '@/components/ui/project-card'
import { ProjectFilter, ProjectSearch, ProjectSort } from '@/components/ui/project-filter'
import { Card, CardContent } from '@/components/ui/card'

const projectsData = [
  {
    id: 1,
    title: "Healthcare Management System",
    description: "Full stack web application built with Java Spring Boot backend and Bootstrap frontend for managing patient records, appointments, and medical history.",
    longDescription: "A comprehensive healthcare management system featuring secure patient data management, appointment scheduling, medical history tracking, and role-based access control. Built using Java Spring Boot for robust backend services and Bootstrap for responsive frontend design.",
    image: "https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=800&h=600&fit=crop&crop=center",
    technologies: ["Java", "Spring Boot", "HTML5", "CSS3", "JavaScript", "Bootstrap", "MySQL", "REST APIs"],
    category: "web",
    githubUrl: "https://github.com",
    liveUrl: "#",
    demoUrl: "#",
    date: "2024",
    team: "College Project",
    status: "completed" as const,
    featured: true
  },
  {
    id: 2,
    title: "E-Commerce Web Application",
    description: "Complete e-commerce platform built with Java Spring Boot and Bootstrap featuring product catalog, shopping cart, and payment integration.",
    longDescription: "A full-featured e-commerce web application with modern UI/UX design. Includes product management, shopping cart functionality, user authentication, order processing, and secure payment integration. Built using Java Spring Boot for backend services and Bootstrap for responsive frontend.",
    image: "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=800&h=600&fit=crop&crop=center",
    technologies: ["Java", "Spring Boot", "HTML5", "CSS3", "JavaScript", "Bootstrap", "MySQL", "Payment Gateway"],
    category: "web",
    githubUrl: "https://github.com",
    liveUrl: "#",
    date: "2024",
    team: "College Project",
    status: "completed" as const,
    featured: false
  },
  {
    id: 3,
    title: "Student Management System",
    description: "Comprehensive web application for managing student records, courses, grades, and attendance with role-based access control.",
    longDescription: "A complete student management system built with Java Spring Boot and Bootstrap. Features include student enrollment, course management, grade tracking, attendance monitoring, and report generation. Implements role-based access for students, teachers, and administrators with secure authentication.",
    image: "https://images.unsplash.com/photo-1523240795612-9a054b0db644?w=800&h=600&fit=crop&crop=center",
    technologies: ["Java", "Spring Boot", "HTML5", "CSS3", "JavaScript", "Bootstrap", "MySQL", "JPA"],
    category: "web",
    githubUrl: "https://github.com",
    liveUrl: "#",
    date: "2024",
    team: "College Project",
    status: "completed" as const,
    featured: false
  },
  {
    id: 4,
    title: "Enterprise Web Applications",
    description: "Professional web applications built during internship using Java Spring Boot backend and Bootstrap frontend with enterprise-grade features.",
    longDescription: "Enterprise-level web applications developed during internship at CAUSEVE TECHNOLOGIES LLP. Features include secure authentication, role-based access control, real-time data processing, and responsive design using Java Spring Boot, HTML5, CSS3, JavaScript, and Bootstrap.",
    image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=600&fit=crop&crop=center",
    technologies: ["Java", "Spring Boot", "HTML5", "CSS3", "JavaScript", "Bootstrap", "MySQL", "REST APIs"],
    category: "web",
    githubUrl: "https://github.com",
    liveUrl: "#",
    date: "2024",
    team: "Internship Project",
    status: "completed" as const,
    featured: false
  },
  {
    id: 5,
    title: "Java Backend Automation System",
    description: "Enterprise automation system built with Java for monitoring, alerting, and task scheduling with email notifications and web dashboard.",
    longDescription: "Developed a comprehensive automation system using Java Spring Boot during internship at CYBERNAUT EDUTECH. Features include automated monitoring, email notification system using JavaMail API, task scheduling, and a web-based dashboard for system management and reporting.",
    image: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=600&fit=crop&crop=center",
    technologies: ["Java", "Spring Boot", "JavaMail API", "HTML5", "CSS3", "JavaScript", "MySQL", "Task Scheduling"],
    category: "system",
    githubUrl: "https://github.com",
    liveUrl: "https://cybernaut.co.in/",
    date: "2024",
    team: "Internship Project",
    status: "completed" as const,
    featured: false
  },
  {
    id: 6,
    title: "Full Stack Portfolio Website",
    description: "Modern, responsive portfolio website showcasing Full Stack development skills with Java backend expertise and frontend technologies.",
    longDescription: "A professional portfolio website built to showcase Full Stack development expertise. Features responsive design using HTML5, CSS3, JavaScript, and Bootstrap, with backend capabilities demonstrated through Java Spring Boot projects. Includes interactive elements, smooth animations, and modern UI/UX design.",
    image: "https://images.unsplash.com/photo-1467232004584-a241de8bcf5d?w=800&h=600&fit=crop&crop=center",
    technologies: ["HTML5", "CSS3", "JavaScript", "Bootstrap", "Java", "Spring Boot"],
    category: "web",
    githubUrl: "https://github.com",
    liveUrl: "#",
    date: "2024",
    team: "Personal Project",
    status: "completed" as const,
    featured: false
  }
]

const categories = [
  { id: 'all', label: 'All Projects', icon: 'Globe', count: projectsData.length },
  { id: 'web', label: 'Full Stack', icon: 'Code', count: projectsData.filter(p => p.category === 'web').length },
  { id: 'system', label: 'Backend', icon: 'Cpu', count: projectsData.filter(p => p.category === 'system').length },
  { id: 'frontend', label: 'Frontend', icon: 'Globe', count: projectsData.filter(p => p.category === 'frontend').length },
  { id: 'enterprise', label: 'Enterprise', icon: 'Database', count: projectsData.filter(p => p.category === 'enterprise').length }
]

export default function Projects() {
  const [activeFilter, setActiveFilter] = useState('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [sortBy, setSortBy] = useState('date')
  const [selectedProject, setSelectedProject] = useState<typeof projectsData[0] | null>(null)
  
  const ref = useRef<HTMLElement>(null)
  const isInView = useInView(ref, { once: true, margin: "-100px" })

  // Filter and sort projects
  const filteredProjects = useMemo(() => {
    let filtered = projectsData

    // Apply category filter
    if (activeFilter !== 'all') {
      filtered = filtered.filter(project => project.category === activeFilter)
    }

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(project =>
        project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        project.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        project.technologies.some(tech => tech.toLowerCase().includes(searchTerm.toLowerCase()))
      )
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.title.localeCompare(b.title)
        case 'category':
          return a.category.localeCompare(b.category)
        case 'status':
          return a.status.localeCompare(b.status)
        case 'date':
        default:
          return new Date(b.date).getTime() - new Date(a.date).getTime()
      }
    })

    return filtered
  }, [activeFilter, searchTerm, sortBy])

  return (
    <section ref={ref} className="py-20 bg-muted/20 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `
            radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(0, 255, 136, 0.05) 0%, transparent 50%)
          `
        }} />
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Section Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.6 }}
        >
          <h2 className="text-4xl md:text-5xl font-bold gradient-text mb-6">
            Featured Projects
          </h2>
          <p className="text-xl text-foreground/80 max-w-3xl mx-auto leading-relaxed">
            A showcase of my Full Stack development expertise through real-world projects spanning
            Java backend development, frontend technologies, and enterprise web applications. Each project demonstrates
            problem-solving skills, clean code practices, and modern development methodologies.
          </p>
        </motion.div>

        {/* Controls */}
        <motion.div
          className="mb-12 space-y-6"
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          {/* Search */}
          <ProjectSearch
            searchTerm={searchTerm}
            onSearchChange={setSearchTerm}
            placeholder="Search projects by name, description, or technology..."
          />

          {/* Filter and Sort */}
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
            <ProjectFilter
              categories={categories}
              activeFilter={activeFilter}
              onFilterChange={setActiveFilter}
            />
            
            <ProjectSort
              sortBy={sortBy}
              onSortChange={setSortBy}
            />
          </div>
        </motion.div>

        {/* Projects Grid */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <AnimatePresence mode="wait">
            {filteredProjects.length > 0 ? (
              <motion.div
                key={`${activeFilter}-${searchTerm}-${sortBy}`}
                className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.3 }}
              >
                {filteredProjects.map((project, index) => (
                  <ProjectCard
                    key={project.id}
                    {...project}
                    delay={index * 100}
                    onViewDetails={() => setSelectedProject(project)}
                  />
                ))}
              </motion.div>
            ) : (
              <motion.div
                className="text-center py-16"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
              >
                <div className="text-6xl mb-4">🔍</div>
                <h3 className="text-xl font-semibold text-foreground mb-2">No projects found</h3>
                <p className="text-foreground/70">
                  Try adjusting your search terms or filters to find what you're looking for.
                </p>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>

        {/* Stats */}
        <motion.div
          className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-6"
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.6, delay: 0.6 }}
        >
          {[
            { label: "Total Projects", value: projectsData.length },
            { label: "Completed", value: projectsData.filter(p => p.status === 'completed').length },
            { label: "Technologies", value: [...new Set(projectsData.flatMap(p => p.technologies))].length },
            { label: "GitHub Stars", value: "150+" }
          ].map((stat, index) => (
            <Card key={stat.label} variant="glass" className="text-center hover:glow-blue transition-all duration-300">
              <CardContent className="p-4">
                <div className="text-2xl font-bold gradient-text mb-1">{stat.value}</div>
                <div className="text-sm text-foreground/70">{stat.label}</div>
              </CardContent>
            </Card>
          ))}
        </motion.div>
      </div>
    </section>
  )
}
