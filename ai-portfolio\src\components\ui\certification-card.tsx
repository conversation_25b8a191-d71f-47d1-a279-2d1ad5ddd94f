"use client"

import { motion, useInView } from 'framer-motion'
import { useRef } from 'react'
import { ExternalLink, Award, Calendar, CheckCircle } from 'lucide-react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'

interface CertificationCardProps {
  title: string
  issuer: string
  date: string
  description?: string
  skills: string[]
  credentialUrl?: string
  image?: string
  status: 'completed' | 'in-progress' | 'planned'
  delay?: number
  className?: string
}

const statusConfig = {
  completed: {
    icon: CheckCircle,
    color: 'text-neon-green',
    bg: 'bg-neon-green/10',
    border: 'border-neon-green/30'
  },
  'in-progress': {
    icon: Calendar,
    color: 'text-cyber-blue',
    bg: 'bg-cyber-blue/10',
    border: 'border-cyber-blue/30'
  },
  planned: {
    icon: Award,
    color: 'text-electric-violet',
    bg: 'bg-electric-violet/10',
    border: 'border-electric-violet/30'
  }
}

export function CertificationCard({
  title,
  issuer,
  date,
  description,
  skills,
  credentialUrl,
  image,
  status,
  delay = 0,
  className
}: CertificationCardProps) {
  const ref = useRef<HTMLDivElement>(null)
  const isInView = useInView(ref, { once: true, margin: "-100px" })
  
  const config = statusConfig[status]
  const StatusIcon = config.icon

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, y: 50 }}
      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
      transition={{ duration: 0.6, delay: delay / 1000 }}
      className={className}
    >
      <Card 
        variant="neural" 
        className={cn(
          "group hover:scale-[1.02] transition-all duration-300 relative overflow-hidden",
          config.border
        )}
      >
        {/* Status Badge */}
        <div className={cn(
          "absolute top-4 right-4 px-2 py-1 rounded-full flex items-center space-x-1 text-xs font-medium",
          config.bg,
          config.color
        )}>
          <StatusIcon className="w-3 h-3" />
          <span className="capitalize">{status.replace('-', ' ')}</span>
        </div>

        <CardContent className="p-6">
          {/* Header */}
          <div className="flex items-start space-x-4 mb-4">
            {image && (
              <div className="flex-shrink-0 w-12 h-12 rounded-lg bg-gradient-to-br from-cyber-blue/20 to-electric-violet/20 flex items-center justify-center">
                <img 
                  src={image} 
                  alt={issuer}
                  className="w-8 h-8 object-contain"
                />
              </div>
            )}
            
            <div className="flex-1 min-w-0">
              <h3 className="text-lg font-semibold text-foreground group-hover:text-primary transition-colors">
                {title}
              </h3>
              <p className="text-sm text-foreground/70 mb-1">{issuer}</p>
              <div className="flex items-center text-xs text-foreground/60">
                <Calendar className="w-3 h-3 mr-1" />
                {date}
              </div>
            </div>
          </div>

          {/* Description */}
          {description && (
            <p className="text-sm text-foreground/80 mb-4 leading-relaxed">
              {description}
            </p>
          )}

          {/* Skills */}
          <div className="mb-4">
            <h4 className="text-sm font-medium text-foreground/90 mb-2">Skills Covered:</h4>
            <div className="flex flex-wrap gap-2">
              {skills.map((skill, index) => (
                <motion.span
                  key={skill}
                  className="px-2 py-1 text-xs bg-primary/10 text-primary rounded-md border border-primary/20"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.8 }}
                  transition={{ duration: 0.3, delay: (delay + index * 100) / 1000 }}
                >
                  {skill}
                </motion.span>
              ))}
            </div>
          </div>

          {/* Action Button */}
          {credentialUrl && status === 'completed' && (
            <Button
              variant="outline"
              size="sm"
              className="w-full group/btn"
              asChild
            >
              <a href={credentialUrl} target="_blank" rel="noopener noreferrer">
                <span>View Credential</span>
                <ExternalLink className="w-3 h-3 ml-2 group-hover/btn:translate-x-1 transition-transform" />
              </a>
            </Button>
          )}

          {/* Progress indicator for in-progress certifications */}
          {status === 'in-progress' && (
            <div className="mt-4">
              <div className="flex justify-between text-xs text-foreground/60 mb-1">
                <span>Progress</span>
                <span>75%</span>
              </div>
              <div className="w-full bg-foreground/10 rounded-full h-1.5">
                <motion.div
                  className="bg-gradient-to-r from-cyber-blue to-hologram-blue h-1.5 rounded-full"
                  initial={{ width: 0 }}
                  animate={isInView ? { width: "75%" } : { width: 0 }}
                  transition={{ duration: 1, delay: (delay + 500) / 1000 }}
                />
              </div>
            </div>
          )}
        </CardContent>

        {/* Hover Effect */}
        <div className="absolute inset-0 bg-gradient-to-r from-primary/5 via-transparent to-secondary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
      </Card>
    </motion.div>
  )
}

interface CertificationGridProps {
  certifications: Array<{
    title: string
    issuer: string
    date: string
    description?: string
    skills: string[]
    credentialUrl?: string
    image?: string
    status: 'completed' | 'in-progress' | 'planned'
  }>
  className?: string
}

export function CertificationGrid({ certifications, className }: CertificationGridProps) {
  return (
    <div className={cn(
      "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",
      className
    )}>
      {certifications.map((cert, index) => (
        <CertificationCard
          key={`${cert.title}-${cert.issuer}`}
          {...cert}
          delay={index * 200}
        />
      ))}
    </div>
  )
}
