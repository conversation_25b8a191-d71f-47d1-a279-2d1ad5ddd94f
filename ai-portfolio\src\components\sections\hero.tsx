"use client"

import { useEffect, useRef, Suspense } from 'react'
import { motion } from 'framer-motion'
import { ChevronDown, Download, Github, Linkedin, Mail } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { TypingAnimation, GradientTypingAnimation } from '@/components/ui/typing-animation'
import { gsap } from 'gsap'
import dynamic from 'next/dynamic'

// Lazy load the 3D background
const NeuralNetworkBackground = dynamic(
  () => import('@/components/3d/neural-network-bg'),
  {
    ssr: false,
    loading: () => <div className="absolute inset-0 -z-10 bg-gradient-to-br from-neural-black/80 via-deep-space/60 to-neural-black/80" />
  }
)

const socialLinks = [
  { icon: Github, href: 'https://github.com/sanjai827054', label: 'GitHub' },
  { icon: Linkedin, href: 'https://linkedin.com/in/sanjai-s-ai', label: 'LinkedIn' },
  { icon: Mail, href: 'mailto:<EMAIL>', label: 'Email' }
]

const roles = [
  "Full Stack Developer",
  "Java Backend Developer",
  "Frontend Web Developer",
  "Bootstrap & JavaScript Expert",
  "HTML/CSS Specialist"
]

export default function Hero() {
  const heroRef = useRef<HTMLElement>(null)
  const titleRef = useRef<HTMLHeadingElement>(null)
  const subtitleRef = useRef<HTMLDivElement>(null)
  const ctaRef = useRef<HTMLDivElement>(null)
  const socialRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Initial setup - hide elements
      gsap.set([titleRef.current, subtitleRef.current, ctaRef.current, socialRef.current], {
        opacity: 0,
        y: 50
      })

      // Create timeline
      const tl = gsap.timeline({ delay: 0.5 })

      tl.to(titleRef.current, {
        opacity: 1,
        y: 0,
        duration: 1,
        ease: "power3.out"
      })
      .to(subtitleRef.current, {
        opacity: 1,
        y: 0,
        duration: 1,
        ease: "power3.out"
      }, "-=0.5")
      .to(ctaRef.current, {
        opacity: 1,
        y: 0,
        duration: 1,
        ease: "power3.out"
      }, "-=0.5")
      .to(socialRef.current, {
        opacity: 1,
        y: 0,
        duration: 1,
        ease: "power3.out"
      }, "-=0.5")

    }, heroRef)

    return () => ctx.revert()
  }, [])

  const scrollToNext = () => {
    const nextSection = document.getElementById('about')
    nextSection?.scrollIntoView({ behavior: 'smooth' })
  }

  return (
    <section 
      ref={heroRef}
      className="relative min-h-screen flex items-center justify-center overflow-hidden neural-bg"
    >
      {/* 3D Background */}
      <Suspense fallback={<div className="absolute inset-0 -z-10 bg-gradient-to-br from-neural-black/80 via-deep-space/60 to-neural-black/80" />}>
        <NeuralNetworkBackground />
      </Suspense>
      
      {/* Gradient Overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-neural-black/80 via-deep-space/60 to-neural-black/80" />
      
      {/* Content */}
      <div className="relative z-10 max-w-6xl mx-auto px-6 text-center">
        {/* Main Title */}
        <motion.h1 
          ref={titleRef}
          className="text-5xl md:text-7xl lg:text-8xl font-bold mb-6"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, delay: 0.5 }}
        >
          <span className="block text-foreground mb-2">Hello, I'm</span>
          <span className="block gradient-text">
            <TypingAnimation
              text="SANJAI S"
              speed={150}
              delay={1500}
            />
          </span>
        </motion.h1>

        {/* Subtitle with Role Animation */}
        <div ref={subtitleRef} className="mb-8">
          <div className="text-xl md:text-2xl lg:text-3xl text-foreground/80 mb-4">
            <GradientTypingAnimation
              text={roles}
              speed={100}
              delay={3000}
              repeat={true}
            />
          </div>
          <p className="text-lg md:text-xl text-foreground/70 max-w-3xl mx-auto leading-relaxed">
            Passionate Full Stack Developer specializing in Java backend development and modern frontend technologies.
            Expert in HTML, CSS, JavaScript, Bootstrap, and Java. Building responsive, scalable web applications
            with clean code and innovative solutions.
          </p>
        </div>

        {/* CTA Buttons */}
        <div ref={ctaRef} className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
          <Button size="lg" variant="primary" className="group">
            <Download className="w-5 h-5 mr-2 group-hover:animate-bounce" />
            Download Resume
          </Button>
          <Button size="lg" variant="outline" onClick={scrollToNext}>
            View My Work
            <ChevronDown className="w-5 h-5 ml-2 animate-bounce" />
          </Button>
        </div>

        {/* Social Links */}
        <div ref={socialRef} className="flex justify-center space-x-6">
          {socialLinks.map(({ icon: Icon, href, label }) => (
            <motion.a
              key={label}
              href={href}
              className="p-3 glass rounded-full hover:glow-blue transition-all duration-300 group"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
              aria-label={label}
            >
              <Icon className="w-6 h-6 text-foreground group-hover:text-primary transition-colors" />
            </motion.a>
          ))}
        </div>

        {/* Floating Elements */}
        <div className="absolute top-20 left-10 w-20 h-20 rounded-full bg-cyber-blue/10 animate-pulse" />
        <div className="absolute top-40 right-20 w-16 h-16 rounded-full bg-electric-violet/10 animate-pulse delay-1000" />
        <div className="absolute bottom-40 left-20 w-12 h-12 rounded-full bg-neon-green/10 animate-pulse delay-2000" />
      </div>

      {/* Scroll Indicator */}
      <motion.div 
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
        animate={{ y: [0, 10, 0] }}
        transition={{ duration: 2, repeat: Infinity }}
      >
        <button 
          onClick={scrollToNext}
          className="flex flex-col items-center text-foreground/60 hover:text-primary transition-colors group"
        >
          <span className="text-sm mb-2 group-hover:text-primary">Scroll Down</span>
          <ChevronDown className="w-6 h-6 animate-bounce" />
        </button>
      </motion.div>

      {/* Decorative Grid */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `
            linear-gradient(rgba(0, 212, 255, 0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1px, transparent 1px)
          `,
          backgroundSize: '50px 50px'
        }} />
      </div>
    </section>
  )
}
