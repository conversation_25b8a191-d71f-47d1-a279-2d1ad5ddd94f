name: Deploy AI Portfolio

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [18.x, 20.x]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run linting
      run: npm run lint

    - name: Run type checking
      run: npx tsc --noEmit

    - name: Build application
      run: npm run build

    - name: Run tests (if available)
      run: npm test --if-present

    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: build-files-${{ matrix.node-version }}
        path: .next/

    # Uncomment and configure these secrets in GitHub for automated Vercel deployment:
    # VERCEL_TOKEN, ORG_ID, PROJECT_ID
    # - name: Deploy to Vercel
    #   if: github.ref == 'refs/heads/main' && matrix.node-version == '20.x'
    #   uses: amondnet/vercel-action@v25
    #   with:
    #     vercel-token: ${{ secrets.VERCEL_TOKEN }}
    #     vercel-org-id: ${{ secrets.ORG_ID }}
    #     vercel-project-id: ${{ secrets.PROJECT_ID }}
    #     vercel-args: '--prod'

    - name: Export static files (for GitHub Pages)
      if: github.ref == 'refs/heads/main' && matrix.node-version == '20.x'
      run: npm run build

    - name: Deploy to GitHub Pages
      if: github.ref == 'refs/heads/main' && matrix.node-version == '20.x'
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./out

  lighthouse:
    needs: build-and-deploy
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Wait for deployment
      run: sleep 60

    - name: Run Lighthouse CI
      uses: treosh/lighthouse-ci-action@v10
      with:
        urls: |
          https://your-portfolio-url.vercel.app
        configPath: './lighthouserc.json'
        uploadArtifacts: true
        temporaryPublicStorage: true

  security-scan:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Run security audit
      run: npm audit --audit-level moderate

    - name: Check for vulnerabilities
      run: npm audit --audit-level high --production
