import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary disabled:pointer-events-none disabled:opacity-50 relative overflow-hidden group",
  {
    variants: {
      variant: {
        default: "glass text-foreground hover:glow-blue hover:scale-105",
        primary: "bg-gradient-to-r from-cyber-blue to-hologram-blue text-white hover:shadow-lg hover:scale-105",
        secondary: "bg-gradient-to-r from-electric-violet to-quantum-purple text-white hover:shadow-lg hover:scale-105",
        accent: "bg-gradient-to-r from-neon-green to-matrix-green text-neural-black hover:shadow-lg hover:scale-105",
        outline: "border-2 border-primary/50 text-primary hover:bg-primary/10 hover:border-primary hover:glow-blue",
        ghost: "text-foreground hover:bg-primary/10 hover:text-primary",
        destructive: "bg-gradient-to-r from-red-500 to-red-600 text-white hover:shadow-lg hover:scale-105",
        glow: "glass text-foreground hover:glow-purple hover:scale-105 border border-electric-violet/30"
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-8 px-3 text-xs",
        lg: "h-12 px-8 text-base",
        xl: "h-14 px-10 text-lg",
        icon: "h-10 w-10"
      }
    },
    defaultVariants: {
      variant: "default",
      size: "default"
    }
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, children, ...props }, ref) => {
    return (
      <button
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      >
        {/* Animated background for gradient buttons */}
        {(variant === "primary" || variant === "secondary" || variant === "accent") && (
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700" />
        )}
        
        {/* Ripple effect */}
        <div className="absolute inset-0 rounded-lg opacity-0 group-active:opacity-100 bg-white/20 transition-opacity duration-150" />
        
        <span className="relative z-10">{children}</span>
      </button>
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
