"use client"

import { useRef } from 'react'
import { motion, useInView } from 'framer-motion'
import { Brain, Code, Database, Cpu, Zap, Target } from 'lucide-react'
import { Card, CardContent, NeuralCard } from '@/components/ui/card'
import { SkillCategory, CircularSkill } from '@/components/ui/skill-bar'
import { CertificationGrid } from '@/components/ui/certification-card'

const skills = {
  frontend: [
    { name: 'HTML5', percentage: 95, color: 'orange' as const, icon: <Code className="w-4 h-4" /> },
    { name: 'CSS3', percentage: 92, color: 'blue' as const, icon: <Code className="w-4 h-4" /> },
    { name: 'JavaScript', percentage: 90, color: 'green' as const, icon: <Code className="w-4 h-4" /> },
    { name: 'Bootstrap', percentage: 95, color: 'purple' as const, icon: <Code className="w-4 h-4" /> },
    { name: 'Responsive Design', percentage: 93, color: 'pink' as const, icon: <Code className="w-4 h-4" /> },
    { name: 'jQuery', percentage: 85, color: 'blue' as const, icon: <Code className="w-4 h-4" /> }
  ],
  backend: [
    { name: 'Java', percentage: 95, color: 'orange' as const, icon: <Code className="w-4 h-4" /> },
    { name: 'Spring Boot', percentage: 88, color: 'green' as const, icon: <Cpu className="w-4 h-4" /> },
    { name: 'REST APIs', percentage: 90, color: 'blue' as const, icon: <Cpu className="w-4 h-4" /> },
    { name: 'Microservices', percentage: 82, color: 'purple' as const, icon: <Cpu className="w-4 h-4" /> },
    { name: 'Maven', percentage: 85, color: 'red' as const, icon: <Code className="w-4 h-4" /> },
    { name: 'JPA/Hibernate', percentage: 80, color: 'pink' as const, icon: <Database className="w-4 h-4" /> }
  ],
  database: [
    { name: 'MySQL', percentage: 90, color: 'blue' as const, icon: <Database className="w-4 h-4" /> },
    { name: 'PostgreSQL', percentage: 85, color: 'green' as const, icon: <Database className="w-4 h-4" /> },
    { name: 'MongoDB', percentage: 80, color: 'purple' as const, icon: <Database className="w-4 h-4" /> },
    { name: 'SQL Server', percentage: 82, color: 'orange' as const, icon: <Database className="w-4 h-4" /> },
    { name: 'Database Design', percentage: 88, color: 'pink' as const, icon: <Database className="w-4 h-4" /> },
    { name: 'JDBC', percentage: 85, color: 'blue' as const, icon: <Database className="w-4 h-4" /> }
  ]
}

const circularSkills = [
  { name: 'Java Backend', percentage: 95, color: '#00d4ff' },
  { name: 'Frontend Dev', percentage: 93, color: '#8b5cf6' },
  { name: 'Full Stack', percentage: 90, color: '#00ff88' },
  { name: 'Database Design', percentage: 88, color: '#ff006e' }
]

const certifications = [
  {
    title: 'Java Full Stack Development Certificate',
    issuer: 'Programming Institute',
    date: 'Completed 2024',
    description: 'Comprehensive Java full stack development course covering Spring Boot, REST APIs, microservices, and enterprise application development.',
    skills: ['Java', 'Spring Boot', 'REST APIs', 'Microservices', 'JPA'],
    status: 'completed' as const,
    credentialUrl: '#'
  },
  {
    title: 'Frontend Web Development Certificate',
    issuer: 'Web Development Institute',
    date: 'Completed 2024',
    description: 'Advanced frontend development course focusing on HTML5, CSS3, JavaScript, Bootstrap, and responsive web design principles.',
    skills: ['HTML5', 'CSS3', 'JavaScript', 'Bootstrap', 'Responsive Design'],
    status: 'completed' as const,
    credentialUrl: '#'
  },
  {
    title: 'Database Management Certificate',
    issuer: 'Database Institute',
    date: 'Completed 2024',
    description: 'Comprehensive database management course covering SQL, database design, optimization, and integration with Java applications.',
    skills: ['SQL', 'MySQL', 'Database Design', 'JDBC', 'Query Optimization'],
    status: 'completed' as const,
    credentialUrl: '#'
  },
  {
    title: 'B.Tech AI & Data Science',
    issuer: 'Mahendra Engineering College',
    date: '2021-2025',
    description: 'Bachelor of Technology in Artificial Intelligence & Data Science with focus on machine learning, deep learning, and data analytics.',
    skills: ['AI', 'Machine Learning', 'Data Science', 'Deep Learning'],
    status: 'in-progress' as const
  },
  {
    title: 'Higher Secondary Certificate (HSC)',
    issuer: 'Sathya Saai Matric Higher Secondary School',
    date: '2020-2021',
    description: 'Higher Secondary education with strong foundation in mathematics and science.',
    skills: ['Mathematics', 'Physics', 'Chemistry', 'Computer Science'],
    status: 'completed' as const
  },
  {
    title: 'Secondary School Leaving Certificate (SSLC)',
    issuer: 'Sathya Saai Matric Higher Secondary School',
    date: '2018-2019',
    description: 'Secondary education with excellent academic performance and strong fundamentals.',
    skills: ['Core Subjects', 'Mathematics', 'Science', 'English'],
    status: 'completed' as const
  }
]

export default function About() {
  const ref = useRef<HTMLElement>(null)
  const isInView = useInView(ref, { once: true, margin: "-100px" })

  return (
    <section ref={ref} className="py-20 bg-muted/20 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `
            radial-gradient(circle at 25% 25%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 75% 75%, rgba(139, 92, 246, 0.1) 0%, transparent 50%)
          `
        }} />
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Section Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.6 }}
        >
          <h2 className="text-4xl md:text-5xl font-bold gradient-text mb-6">
            About Me
          </h2>
          <p className="text-xl text-foreground/80 max-w-3xl mx-auto leading-relaxed">
            I am SANJAI S, a passionate Full Stack Developer specializing in Java backend development
            and modern frontend technologies. Expert in building scalable web applications using HTML, CSS,
            JavaScript, Bootstrap, and Java. I consider myself responsible and detail-oriented, ready to contribute to innovative projects.
          </p>
        </motion.div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16">
          {/* Personal Info Card */}
          <motion.div
            className="lg:col-span-1"
            initial={{ opacity: 0, x: -50 }}
            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -50 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <NeuralCard className="h-full">
              <CardContent className="p-6">
                <div className="text-center mb-6">
                  <div className="w-32 h-32 mx-auto mb-4 rounded-full bg-gradient-to-br from-cyber-blue/20 to-electric-violet/20 flex items-center justify-center">
                    <Brain className="w-16 h-16 text-primary" />
                  </div>
                  <h3 className="text-2xl font-bold text-foreground mb-2">SANJAI S</h3>
                  <p className="text-primary font-medium">Full Stack Developer</p>
                  <p className="text-foreground/70 text-sm">Java Backend & Frontend Specialist</p>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <Target className="w-5 h-5 text-primary" />
                    <div>
                      <p className="font-medium text-foreground">Specialization</p>
                      <p className="text-sm text-foreground/70">Java Backend, Frontend Development, Full Stack</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <Zap className="w-5 h-5 text-primary" />
                    <div>
                      <p className="font-medium text-foreground">Passion</p>
                      <p className="text-sm text-foreground/70">Innovation & Problem Solving</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </NeuralCard>
          </motion.div>

          {/* Circular Skills */}
          <motion.div
            className="lg:col-span-2"
            initial={{ opacity: 0, x: 50 }}
            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -50 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <Card variant="glow" className="h-full">
              <CardContent className="p-6">
                <h3 className="text-xl font-semibold text-foreground mb-6 text-center">
                  Core Competencies
                </h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                  {circularSkills.map((skill, index) => (
                    <CircularSkill
                      key={skill.name}
                      name={skill.name}
                      percentage={skill.percentage}
                      color={skill.color}
                      delay={index * 200}
                    />
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Skills Section */}
        <motion.div
          className="mb-16"
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.6, delay: 0.6 }}
        >
          <h3 className="text-3xl font-bold text-center gradient-text mb-12">
            Technical Skills
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <SkillCategory
              title="Frontend Technologies"
              skills={skills.frontend}
            />
            <SkillCategory
              title="Backend & Java"
              skills={skills.backend}
            />
            <SkillCategory
              title="Database & Tools"
              skills={skills.database}
            />
          </div>
        </motion.div>

        {/* Certifications Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.6, delay: 0.8 }}
        >
          <h3 className="text-3xl font-bold text-center gradient-text mb-12">
            Certifications & Courses
          </h3>
          
          <CertificationGrid certifications={certifications} />
        </motion.div>
      </div>
    </section>
  )
}
