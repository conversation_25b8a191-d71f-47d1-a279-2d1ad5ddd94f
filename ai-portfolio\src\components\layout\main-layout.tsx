"use client"

import { ReactNode } from 'react'
import Navbar from '@/components/navigation/navbar'
import { ScrollProgress, CircularScrollProgress, ScrollToTop } from '@/components/ui/scroll-progress'
import { WebVitals, PerformanceMonitor, PreloadResources, ServiceWorkerRegistration } from '@/components/performance/web-vitals'

interface MainLayoutProps {
  children: ReactNode
}

export default function MainLayout({ children }: MainLayoutProps) {
  return (
    <div className="relative min-h-screen">
      {/* Performance Monitoring */}
      <WebVitals />
      <PerformanceMonitor />
      <PreloadResources />
      <ServiceWorkerRegistration />

      {/* Scroll Progress Bar */}
      <ScrollProgress />

      {/* Navigation */}
      <Navbar />

      {/* Main Content */}
      <main className="relative">
        {children}
      </main>

      {/* Scroll Indicators */}
      <CircularScrollProgress />
      <ScrollToTop />
    </div>
  )
}
