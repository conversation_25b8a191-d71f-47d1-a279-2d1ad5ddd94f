"use client"

import { motion } from 'framer-motion'
import { Brain, Code, Globe, Smartphone, Database, Cpu } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'

interface FilterOption {
  id: string
  label: string
  icon: React.ComponentType<{ className?: string }>
  count: number
}

interface ProjectFilterProps {
  categories: FilterOption[]
  activeFilter: string
  onFilterChange: (filter: string) => void
  className?: string
}

const iconMap = {
  all: Globe,
  ai: Brain,
  web: Code,
  mobile: Smartphone,
  data: Database,
  system: Cpu
}

export function ProjectFilter({ 
  categories, 
  activeFilter, 
  onFilterChange, 
  className 
}: ProjectFilterProps) {
  return (
    <div className={cn("flex flex-wrap gap-3 justify-center", className)}>
      {categories.map((category, index) => {
        const Icon = iconMap[category.id as keyof typeof iconMap] || Globe
        const isActive = activeFilter === category.id

        return (
          <motion.div
            key={category.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
          >
            <Button
              variant={isActive ? "primary" : "outline"}
              size="sm"
              onClick={() => onFilterChange(category.id)}
              className={cn(
                "group relative overflow-hidden transition-all duration-300",
                isActive 
                  ? "shadow-lg scale-105" 
                  : "hover:scale-105 hover:shadow-md"
              )}
            >
              {/* Background Animation */}
              {!isActive && (
                <div className="absolute inset-0 bg-gradient-to-r from-primary/10 to-secondary/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              )}
              
              <div className="relative flex items-center space-x-2">
                <Icon className="w-4 h-4" />
                <span className="font-medium">{category.label}</span>
                <span className={cn(
                  "px-1.5 py-0.5 text-xs rounded-full",
                  isActive 
                    ? "bg-white/20 text-white" 
                    : "bg-primary/10 text-primary"
                )}>
                  {category.count}
                </span>
              </div>

              {/* Active Indicator */}
              {isActive && (
                <motion.div
                  className="absolute bottom-0 left-0 right-0 h-0.5 bg-white"
                  layoutId="activeFilter"
                  initial={false}
                  transition={{ type: "spring", stiffness: 500, damping: 30 }}
                />
              )}
            </Button>
          </motion.div>
        )
      })}
    </div>
  )
}

// Search component for projects
interface ProjectSearchProps {
  searchTerm: string
  onSearchChange: (term: string) => void
  placeholder?: string
  className?: string
}

export function ProjectSearch({ 
  searchTerm, 
  onSearchChange, 
  placeholder = "Search projects...",
  className 
}: ProjectSearchProps) {
  return (
    <div className={cn("relative max-w-md mx-auto", className)}>
      <div className="relative">
        <input
          type="text"
          value={searchTerm}
          onChange={(e) => onSearchChange(e.target.value)}
          placeholder={placeholder}
          className={cn(
            "w-full px-4 py-3 pl-12 rounded-lg border border-border/50 bg-background/50 backdrop-blur-sm",
            "text-foreground placeholder:text-foreground/50",
            "focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary/50",
            "transition-all duration-300"
          )}
        />
        
        {/* Search Icon */}
        <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
          <svg 
            className="w-5 h-5 text-foreground/50" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" 
            />
          </svg>
        </div>

        {/* Clear Button */}
        {searchTerm && (
          <button
            onClick={() => onSearchChange('')}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 p-1 rounded-full hover:bg-foreground/10 transition-colors"
          >
            <svg 
              className="w-4 h-4 text-foreground/50" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M6 18L18 6M6 6l12 12" 
              />
            </svg>
          </button>
        )}
      </div>

      {/* Search Results Count */}
      {searchTerm && (
        <motion.div
          className="absolute top-full mt-2 left-0 right-0 text-center text-sm text-foreground/60"
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.2 }}
        >
          Searching for "{searchTerm}"
        </motion.div>
      )}
    </div>
  )
}

// Sort component for projects
interface ProjectSortProps {
  sortBy: string
  onSortChange: (sort: string) => void
  className?: string
}

const sortOptions = [
  { value: 'date', label: 'Latest First' },
  { value: 'name', label: 'Name A-Z' },
  { value: 'category', label: 'Category' },
  { value: 'status', label: 'Status' }
]

export function ProjectSort({ sortBy, onSortChange, className }: ProjectSortProps) {
  return (
    <div className={cn("flex items-center space-x-2", className)}>
      <span className="text-sm text-foreground/70">Sort by:</span>
      <select
        value={sortBy}
        onChange={(e) => onSortChange(e.target.value)}
        className={cn(
          "px-3 py-2 rounded-lg border border-border/50 bg-background/50 backdrop-blur-sm",
          "text-foreground text-sm",
          "focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary/50",
          "transition-all duration-300"
        )}
      >
        {sortOptions.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
    </div>
  )
}
