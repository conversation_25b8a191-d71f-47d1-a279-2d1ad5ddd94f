@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&family=JetBrains+Mono:wght@100;200;300;400;500;600;700;800&display=swap');

:root {
  /* AI-Inspired Color Palette */
  --neural-black: #0a0a0a;
  --cyber-blue: #00d4ff;
  --electric-violet: #8b5cf6;
  --neon-green: #00ff88;
  --quantum-purple: #a855f7;
  --matrix-green: #39ff14;
  --deep-space: #0f0f23;
  --silver-chrome: #e5e7eb;
  --plasma-pink: #ff006e;
  --hologram-blue: #0ea5e9;

  /* Glassmorphism Variables */
  --glass-bg: rgba(255, 255, 255, 0.05);
  --glass-border: rgba(255, 255, 255, 0.1);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);

  /* Dark Theme (Default) */
  --background: var(--neural-black);
  --foreground: var(--silver-chrome);
  --primary: var(--cyber-blue);
  --secondary: var(--electric-violet);
  --accent: var(--neon-green);
  --muted: var(--deep-space);
  --card-bg: rgba(15, 15, 35, 0.8);
  --border: rgba(0, 212, 255, 0.2);
}

[data-theme="light"] {
  /* Light Theme Override */
  --background: #ffffff;
  --foreground: var(--neural-black);
  --primary: var(--hologram-blue);
  --secondary: var(--quantum-purple);
  --accent: var(--plasma-pink);
  --muted: #f8fafc;
  --card-bg: rgba(255, 255, 255, 0.8);
  --border: rgba(139, 92, 246, 0.2);
  --glass-bg: rgba(255, 255, 255, 0.25);
  --glass-border: rgba(255, 255, 255, 0.18);
}

@theme inline {
  /* Colors */
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-secondary: var(--secondary);
  --color-accent: var(--accent);
  --color-muted: var(--muted);
  --color-card: var(--card-bg);
  --color-border: var(--border);

  /* AI Color Palette */
  --color-neural-black: var(--neural-black);
  --color-cyber-blue: var(--cyber-blue);
  --color-electric-violet: var(--electric-violet);
  --color-neon-green: var(--neon-green);
  --color-quantum-purple: var(--quantum-purple);
  --color-matrix-green: var(--matrix-green);
  --color-deep-space: var(--deep-space);
  --color-silver-chrome: var(--silver-chrome);
  --color-plasma-pink: var(--plasma-pink);
  --color-hologram-blue: var(--hologram-blue);

  /* Typography */
  --font-sans: 'Inter', system-ui, -apple-system, sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', monospace;

  /* Spacing & Sizing */
  --radius: 0.75rem;
  --radius-lg: 1rem;
  --radius-xl: 1.5rem;

  /* Shadows */
  --shadow-glow: 0 0 20px rgba(0, 212, 255, 0.3);
  --shadow-glow-purple: 0 0 20px rgba(139, 92, 246, 0.3);
  --shadow-glow-green: 0 0 20px rgba(0, 255, 136, 0.3);
}

/* Global Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans);
  line-height: 1.6;
  overflow-x: hidden;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Glassmorphism Utility Classes */
.glass {
  background: var(--glass-bg);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
}

.glass-card {
  @apply glass rounded-xl p-6;
}

/* Glow Effects */
.glow-blue {
  box-shadow: var(--shadow-glow);
}

.glow-purple {
  box-shadow: var(--shadow-glow-purple);
}

.glow-green {
  box-shadow: var(--shadow-glow-green);
}

/* Animated Gradient Text */
.gradient-text {
  background: linear-gradient(
    45deg,
    var(--cyber-blue),
    var(--electric-violet),
    var(--neon-green),
    var(--plasma-pink)
  );
  background-size: 400% 400%;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradientShift 4s ease-in-out infinite;
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* Neural Network Animation */
.neural-bg {
  position: relative;
  overflow: hidden;
}

.neural-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(0, 255, 136, 0.05) 0%, transparent 50%);
  animation: neuralPulse 6s ease-in-out infinite;
}

@keyframes neuralPulse {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 0.6; transform: scale(1.05); }
}

/* Performance Optimizations */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .gradient-text {
    animation: none;
  }

  .neural-bg::before {
    animation: none;
  }
}

/* Shimmer animation for loading states */
@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--deep-space);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, var(--cyber-blue), var(--electric-violet));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, var(--electric-violet), var(--cyber-blue));
}

/* Image optimization */
img {
  max-width: 100%;
  height: auto;
}

/* Lazy loading optimization */
.lazy-load {
  opacity: 0;
  transition: opacity 0.3s;
}

.lazy-load.loaded {
  opacity: 1;
}
