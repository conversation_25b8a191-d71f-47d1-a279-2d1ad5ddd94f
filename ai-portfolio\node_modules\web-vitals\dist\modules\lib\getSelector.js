/*
 * Copyright 2022 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
const getName = (node) => {
    const name = node.nodeName;
    return node.nodeType === 1
        ? name.toLowerCase()
        : name.toUpperCase().replace(/^#/, '');
};
const MAX_LEN = 100;
export const getSelector = (node) => {
    let sel = '';
    try {
        while (node?.nodeType !== 9) {
            const el = node;
            const part = el.id
                ? '#' + el.id
                : [getName(el), ...Array.from(el.classList).sort()].join('.');
            if (sel.length + part.length > MAX_LEN - 1) {
                return sel || part;
            }
            sel = sel ? part + '>' + sel : part;
            if (el.id) {
                break;
            }
            node = el.parentNode;
        }
    }
    catch {
        // Do nothing...
    }
    return sel;
};
