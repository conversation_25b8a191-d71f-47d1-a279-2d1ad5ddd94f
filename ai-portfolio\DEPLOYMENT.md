# 🚀 AI Portfolio Deployment Guide

This guide will help you deploy your AI portfolio to various hosting platforms.

## 🎯 Quick Start (Recommended)

### Option 1: Vercel (Easiest & Best Performance)

1. **Install Vercel CLI:**
   ```bash
   npm install -g vercel
   ```

2. **Deploy:**
   ```bash
   cd ai-portfolio
   npm run deploy
   # Select option 1 (Vercel)
   ```

3. **Follow prompts:**
   - Login to Vercel when prompted
   - Choose your deployment settings
   - Get your live URL!

### Option 2: One-Click Vercel Deploy

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/yourusername/ai-portfolio)

## 📋 Detailed Deployment Options

### 🔵 Vercel Deployment

**Why Vercel?**
- ✅ Optimized for Next.js
- ✅ Automatic HTTPS
- ✅ Global CDN
- ✅ Free custom domains
- ✅ Automatic deployments from Git

**Steps:**
1. Create account at [vercel.com](https://vercel.com)
2. Connect your GitHub repository
3. Deploy automatically on every push

**Manual Deployment:**
```bash
# Install CLI
npm install -g vercel

# Login
vercel login

# Deploy
vercel --prod
```

### 🟠 Netlify Deployment

**Steps:**
1. Create account at [netlify.com](https://netlify.com)
2. Drag & drop your `out` folder after running `npm run export`
3. Or connect your GitHub repository

**CLI Deployment:**
```bash
# Install CLI
npm install -g netlify-cli

# Login
netlify login

# Build and deploy
npm run build
netlify deploy --prod --dir=out
```

### 🟣 GitHub Pages

**Steps:**
1. Push your code to GitHub
2. Go to repository Settings → Pages
3. Select "Deploy from a branch"
4. Choose `gh-pages` branch
5. The GitHub Action will handle the rest

**Manual Setup:**
```bash
# Build for static export
npm run export

# Push the out folder to gh-pages branch
git subtree push --prefix out origin gh-pages
```

### 🔴 Custom Server

**Requirements:**
- Node.js 18+
- PM2 (recommended for production)

**Steps:**
```bash
# On your server
git clone your-repo
cd ai-portfolio
npm install --production
npm run build

# Start with PM2
npm install -g pm2
pm2 start npm --name "ai-portfolio" -- start
pm2 save
pm2 startup
```

## 🔧 Environment Setup

### Required Environment Variables

Create a `.env.local` file:
```env
# Optional: Analytics
NEXT_PUBLIC_GA_ID=your-google-analytics-id

# Optional: Contact form (if using external service)
NEXT_PUBLIC_CONTACT_API=your-contact-api-endpoint

# Optional: Custom domain
NEXT_PUBLIC_SITE_URL=https://yourportfolio.com
```

### Vercel Environment Variables

In Vercel dashboard → Settings → Environment Variables:
- `NEXT_PUBLIC_GA_ID` (optional)
- `NEXT_PUBLIC_SITE_URL` (optional)

## 🔐 GitHub Secrets (for automated deployment)

If you want automated Vercel deployment via GitHub Actions:

1. **Get Vercel Token:**
   - Go to Vercel → Settings → Tokens
   - Create new token
   - Copy the token

2. **Get Project IDs:**
   ```bash
   vercel link
   # This creates .vercel/project.json with your IDs
   ```

3. **Add GitHub Secrets:**
   - Go to GitHub repo → Settings → Secrets
   - Add these secrets:
     - `VERCEL_TOKEN`: Your Vercel token
     - `ORG_ID`: From .vercel/project.json
     - `PROJECT_ID`: From .vercel/project.json

4. **Uncomment GitHub Action:**
   - Edit `.github/workflows/deploy.yml`
   - Uncomment the Vercel deployment section

## 🌐 Custom Domain Setup

### Vercel Custom Domain
1. Go to Vercel project → Settings → Domains
2. Add your domain
3. Update DNS records as instructed

### Netlify Custom Domain
1. Go to Netlify site → Domain settings
2. Add custom domain
3. Update DNS records

### Cloudflare (recommended for additional features)
1. Add your domain to Cloudflare
2. Update nameservers
3. Configure DNS to point to your hosting provider
4. Enable security and performance features

## 📊 Performance Monitoring

### Lighthouse CI
The project includes Lighthouse CI for performance monitoring:
```bash
npm install -g @lhci/cli
lhci autorun
```

### Web Vitals
Built-in Web Vitals monitoring is included. Check browser console for metrics.

## 🔍 Troubleshooting

### Build Errors
```bash
# Clear cache and reinstall
rm -rf .next node_modules package-lock.json
npm install
npm run build
```

### 3D Components Not Loading
- Ensure Three.js components are properly lazy-loaded
- Check browser console for WebGL errors
- Verify device supports WebGL

### Performance Issues
- Enable production optimizations in next.config.ts
- Optimize images (use WebP/AVIF)
- Check bundle size with `npm run analyze`

## 📈 Post-Deployment Checklist

- [ ] Test on mobile devices
- [ ] Verify all links work
- [ ] Check contact form functionality
- [ ] Test 3D animations
- [ ] Verify social media links
- [ ] Check loading performance
- [ ] Test dark/light theme toggle
- [ ] Verify SEO meta tags
- [ ] Set up analytics (optional)
- [ ] Configure custom domain (optional)

## 🆘 Need Help?

1. **Check the logs:**
   - Vercel: Project dashboard → Functions tab
   - Netlify: Site dashboard → Deploys tab
   - GitHub: Actions tab

2. **Common issues:**
   - Build failures: Check Node.js version compatibility
   - 3D not loading: Browser WebGL support
   - Slow loading: Image optimization needed

3. **Get support:**
   - Vercel: [vercel.com/support](https://vercel.com/support)
   - Netlify: [netlify.com/support](https://netlify.com/support)
   - GitHub: Repository issues

---

🎉 **Your AI Portfolio is ready to impress the world!**
