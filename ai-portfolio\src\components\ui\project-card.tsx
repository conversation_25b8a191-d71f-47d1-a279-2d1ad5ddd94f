"use client"

import { useState, useRef } from 'react'
import { motion, useInView } from 'framer-motion'
import { ExternalLink, Github, Play, Eye, Calendar, Users } from 'lucide-react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'

interface ProjectCardProps {
  title: string
  description: string
  longDescription?: string
  image: string
  technologies: string[]
  category: string
  githubUrl?: string
  liveUrl?: string
  demoUrl?: string
  date: string
  team?: string
  status: 'completed' | 'in-progress' | 'planned'
  featured?: boolean
  delay?: number
  onViewDetails?: () => void
  className?: string
}

const statusConfig = {
  completed: {
    color: 'text-neon-green',
    bg: 'bg-neon-green/10',
    border: 'border-neon-green/30',
    label: 'Completed'
  },
  'in-progress': {
    color: 'text-cyber-blue',
    bg: 'bg-cyber-blue/10',
    border: 'border-cyber-blue/30',
    label: 'In Progress'
  },
  planned: {
    color: 'text-electric-violet',
    bg: 'bg-electric-violet/10',
    border: 'border-electric-violet/30',
    label: 'Planned'
  }
}

export function ProjectCard({
  title,
  description,
  longDescription,
  image,
  technologies,
  category,
  githubUrl,
  liveUrl,
  demoUrl,
  date,
  team,
  status,
  featured = false,
  delay = 0,
  onViewDetails,
  className
}: ProjectCardProps) {
  const [isHovered, setIsHovered] = useState(false)
  const ref = useRef<HTMLDivElement>(null)
  const isInView = useInView(ref, { once: true, margin: "-100px" })
  
  const config = statusConfig[status]

  return (
    <motion.div
      ref={ref}
      className={cn(
        "group relative",
        featured && "md:col-span-2 md:row-span-2",
        className
      )}
      initial={{ opacity: 0, y: 50 }}
      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
      transition={{ duration: 0.6, delay: delay / 1000 }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <Card 
        variant="neural" 
        className={cn(
          "h-full overflow-hidden transition-all duration-500 cursor-pointer",
          "hover:scale-[1.02] hover:-translate-y-2 hover:shadow-2xl",
          featured && "hover:scale-[1.01]",
          config.border
        )}
        onClick={onViewDetails}
      >
        {/* Image Container */}
        <div className="relative overflow-hidden">
          <div className={cn(
            "aspect-video bg-gradient-to-br from-cyber-blue/20 to-electric-violet/20 relative",
            featured && "aspect-[16/10]"
          )}>
            {/* Project image or placeholder */}
            {image ? (
              <img
                src={image}
                alt={title}
                className="absolute inset-0 w-full h-full object-cover"
              />
            ) : (
              <div className="absolute inset-0 flex items-center justify-center text-6xl opacity-50">
                🚀
              </div>
            )}
            
            {/* Status Badge */}
            <div className={cn(
              "absolute top-4 left-4 px-2 py-1 rounded-full text-xs font-medium",
              config.bg,
              config.color
            )}>
              {config.label}
            </div>

            {/* Category Badge */}
            <div className="absolute top-4 right-4 px-2 py-1 rounded-full bg-background/80 backdrop-blur-sm text-xs font-medium text-foreground">
              {category}
            </div>

            {/* Hover Overlay */}
            <motion.div
              className="absolute inset-0 bg-gradient-to-t from-neural-black/80 via-transparent to-transparent flex items-end justify-center pb-6"
              initial={{ opacity: 0 }}
              animate={{ opacity: isHovered ? 1 : 0 }}
              transition={{ duration: 0.3 }}
            >
              <div className="flex space-x-2">
                {githubUrl && (
                  <Button
                    size="sm"
                    variant="ghost"
                    className="bg-background/20 backdrop-blur-sm hover:bg-background/40"
                    onClick={(e) => {
                      e.stopPropagation()
                      window.open(githubUrl, '_blank')
                    }}
                  >
                    <Github className="w-4 h-4" />
                  </Button>
                )}
                {liveUrl && (
                  <Button
                    size="sm"
                    variant="ghost"
                    className="bg-background/20 backdrop-blur-sm hover:bg-background/40"
                    onClick={(e) => {
                      e.stopPropagation()
                      window.open(liveUrl, '_blank')
                    }}
                  >
                    <ExternalLink className="w-4 h-4" />
                  </Button>
                )}
                {demoUrl && (
                  <Button
                    size="sm"
                    variant="ghost"
                    className="bg-background/20 backdrop-blur-sm hover:bg-background/40"
                    onClick={(e) => {
                      e.stopPropagation()
                      window.open(demoUrl, '_blank')
                    }}
                  >
                    <Play className="w-4 h-4" />
                  </Button>
                )}
              </div>
            </motion.div>
          </div>
        </div>

        <CardContent className="p-6">
          {/* Header */}
          <div className="mb-4">
            <h3 className={cn(
              "font-bold text-foreground group-hover:text-primary transition-colors mb-2",
              featured ? "text-2xl" : "text-xl"
            )}>
              {title}
            </h3>
            
            <div className="flex items-center space-x-4 text-sm text-foreground/60 mb-3">
              <div className="flex items-center space-x-1">
                <Calendar className="w-3 h-3" />
                <span>{date}</span>
              </div>
              {team && (
                <div className="flex items-center space-x-1">
                  <Users className="w-3 h-3" />
                  <span>{team}</span>
                </div>
              )}
            </div>
          </div>

          {/* Description */}
          <p className={cn(
            "text-foreground/80 mb-4 leading-relaxed",
            featured ? "text-base" : "text-sm"
          )}>
            {description}
          </p>

          {/* Technologies */}
          <div className="mb-4">
            <div className="flex flex-wrap gap-2">
              {technologies.slice(0, featured ? 8 : 5).map((tech, index) => (
                <motion.span
                  key={tech}
                  className="px-2 py-1 text-xs bg-primary/10 text-primary rounded-md border border-primary/20"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.8 }}
                  transition={{ duration: 0.3, delay: (delay + 200 + index * 50) / 1000 }}
                >
                  {tech}
                </motion.span>
              ))}
              {technologies.length > (featured ? 8 : 5) && (
                <span className="px-2 py-1 text-xs text-foreground/60 rounded-md">
                  +{technologies.length - (featured ? 8 : 5)} more
                </span>
              )}
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center justify-between">
            <Button
              variant="outline"
              size="sm"
              className="group/btn"
              onClick={(e) => {
                e.stopPropagation()
                onViewDetails?.()
              }}
            >
              <Eye className="w-3 h-3 mr-2" />
              <span>View Details</span>
            </Button>

            <div className="flex space-x-2">
              {githubUrl && (
                <Button
                  variant="ghost"
                  size="icon"
                  className="w-8 h-8"
                  onClick={(e) => {
                    e.stopPropagation()
                    window.open(githubUrl, '_blank')
                  }}
                >
                  <Github className="w-4 h-4" />
                </Button>
              )}
              {liveUrl && (
                <Button
                  variant="ghost"
                  size="icon"
                  className="w-8 h-8"
                  onClick={(e) => {
                    e.stopPropagation()
                    window.open(liveUrl, '_blank')
                  }}
                >
                  <ExternalLink className="w-4 h-4" />
                </Button>
              )}
            </div>
          </div>
        </CardContent>

        {/* Featured Badge */}
        {featured && (
          <div className="absolute top-6 left-6 px-3 py-1 bg-gradient-to-r from-cyber-blue to-electric-violet text-white text-xs font-bold rounded-full">
            Featured
          </div>
        )}

        {/* Glow Effect */}
        <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-primary/10 via-transparent to-secondary/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
      </Card>
    </motion.div>
  )
}
