"use client"

import { useRef } from 'react'
import { motion, useInView } from 'framer-motion'
import { Mail, Phone, MapPin, Github, Linkedin, Twitter, Calendar, MessageCircle } from 'lucide-react'
import { ContactForm } from '@/components/ui/contact-form'
import { Card, CardContent, NeuralCard } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

const contactInfo = [
  {
    icon: Mail,
    label: 'Email',
    value: '<EMAIL>',
    href: 'mailto:<EMAIL>',
    description: 'Send me an email anytime'
  },
  {
    icon: Phone,
    label: 'Phone',
    value: '+91 8072686247',
    href: 'tel:+************',
    description: 'Call me for urgent matters'
  },
  {
    icon: MapPin,
    label: 'Location',
    value: 'Tamil Nadu, India',
    href: '#',
    description: 'Available for remote work'
  },
  {
    icon: Calendar,
    label: 'Schedule',
    value: 'Book a Meeting',
    href: 'mailto:<EMAIL>',
    description: 'Let\'s schedule a call'
  }
]

const socialLinks = [
  {
    icon: Github,
    label: 'GitHub',
    href: 'https://github.com/sanjai827054',
    color: 'hover:text-foreground'
  },
  {
    icon: Linkedin,
    label: 'LinkedIn',
    href: 'https://linkedin.com/in/sanjai-s-ai',
    color: 'hover:text-blue-500'
  },
  {
    icon: Mail,
    label: 'Email',
    href: 'mailto:<EMAIL>',
    color: 'hover:text-green-400'
  },
  {
    icon: Phone,
    label: 'Phone',
    href: 'tel:+************',
    color: 'hover:text-indigo-500'
  }
]

const quickActions = [
  {
    title: 'Hire Me',
    description: 'Looking for a Full Stack Java Developer?',
    action: 'Get In Touch',
    color: 'from-cyber-blue to-hologram-blue'
  },
  {
    title: 'Collaborate',
    description: 'Need help with web development projects?',
    action: 'Let\'s Discuss',
    color: 'from-electric-violet to-quantum-purple'
  },
  {
    title: 'Learn Together',
    description: 'Want to discuss Java and web technologies?',
    action: 'Connect Now',
    color: 'from-neon-green to-matrix-green'
  }
]

export default function Contact() {
  const ref = useRef<HTMLElement>(null)
  const isInView = useInView(ref, { once: true, margin: "-100px" })

  return (
    <section ref={ref} className="py-20 bg-background relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `
            radial-gradient(circle at 25% 25%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 75% 75%, rgba(139, 92, 246, 0.1) 0%, transparent 50%)
          `
        }} />
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Section Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.6 }}
        >
          <h2 className="text-4xl md:text-5xl font-bold gradient-text mb-6">
            Let's Work Together
          </h2>
          <p className="text-xl text-foreground/80 max-w-3xl mx-auto leading-relaxed">
            Ready to bring your ideas to life? Whether you're looking for a Full Stack Java Developer,
            frontend specialist, or just want to chat about web development and technology, I'm here to help.
          </p>
        </motion.div>

        {/* Quick Actions */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-16"
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          {quickActions.map((action, index) => (
            <motion.div
              key={action.title}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.9 }}
              transition={{ duration: 0.4, delay: 0.1 * index }}
            >
              <Card variant="glass" className="text-center hover:scale-105 transition-all duration-300 group cursor-pointer">
                <CardContent className="p-6">
                  <div className={`w-12 h-12 mx-auto mb-4 rounded-full bg-gradient-to-r ${action.color} flex items-center justify-center`}>
                    <span className="text-white text-xl">💼</span>
                  </div>
                  <h3 className="text-lg font-semibold text-foreground mb-2">{action.title}</h3>
                  <p className="text-sm text-foreground/70 mb-4">{action.description}</p>
                  <Button variant="outline" size="sm" className="group-hover:bg-primary/10">
                    {action.action}
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Contact Form */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -50 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <ContactForm />
          </motion.div>

          {/* Contact Information */}
          <motion.div
            className="space-y-8"
            initial={{ opacity: 0, x: 50 }}
            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: 50 }}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            {/* Contact Info Cards */}
            <div className="space-y-4">
              <h3 className="text-2xl font-bold text-foreground mb-6">Get In Touch</h3>
              {contactInfo.map((info, index) => {
                const Icon = info.icon
                return (
                  <motion.div
                    key={info.label}
                    initial={{ opacity: 0, y: 20 }}
                    animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                    transition={{ duration: 0.4, delay: 0.1 * index }}
                  >
                    <NeuralCard className="hover:scale-[1.02] transition-all duration-300 group">
                      <CardContent className="p-4">
                        <div className="flex items-center space-x-4">
                          <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center group-hover:bg-primary/20 transition-colors">
                            <Icon className="w-6 h-6 text-primary" />
                          </div>
                          <div className="flex-1">
                            <h4 className="font-semibold text-foreground">{info.label}</h4>
                            <p className="text-sm text-foreground/70 mb-1">{info.description}</p>
                            <a
                              href={info.href}
                              className="text-primary hover:text-primary/80 transition-colors font-medium"
                            >
                              {info.value}
                            </a>
                          </div>
                        </div>
                      </CardContent>
                    </NeuralCard>
                  </motion.div>
                )
              })}
            </div>

            {/* Social Links */}
            <div>
              <h3 className="text-xl font-bold text-foreground mb-4">Connect With Me</h3>
              <div className="flex space-x-4">
                {socialLinks.map((social, index) => {
                  const Icon = social.icon
                  return (
                    <motion.a
                      key={social.label}
                      href={social.href}
                      target="_blank"
                      rel="noopener noreferrer"
                      className={`p-3 glass rounded-full hover:glow-blue transition-all duration-300 group ${social.color}`}
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.95 }}
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.8 }}
                      transition={{ duration: 0.3, delay: 0.1 * index }}
                    >
                      <Icon className="w-6 h-6 text-foreground group-hover:text-primary transition-colors" />
                    </motion.a>
                  )
                })}
              </div>
            </div>

            {/* Availability Status */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
              transition={{ duration: 0.6, delay: 0.8 }}
            >
              <Card variant="gradient" className="border border-neon-green/30">
                <CardContent className="p-6">
                  <div className="flex items-center space-x-3 mb-3">
                    <div className="w-3 h-3 bg-neon-green rounded-full animate-pulse" />
                    <span className="font-semibold text-foreground">Available for Work</span>
                  </div>
                  <p className="text-sm text-foreground/80 leading-relaxed">
                    I'm currently seeking opportunities for my first work experience in Full Stack development.
                    Whether it's internships, entry-level Java developer positions, or collaborative web development projects,
                    I'd love to discuss how I can contribute to innovative software solutions.
                  </p>
                </CardContent>
              </Card>
            </motion.div>

            {/* Response Time */}
            <motion.div
              className="text-center p-4 glass rounded-lg"
              initial={{ opacity: 0, y: 20 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
              transition={{ duration: 0.6, delay: 1 }}
            >
              <div className="text-2xl mb-2">⚡</div>
              <p className="text-sm text-foreground/70">
                <span className="font-semibold text-primary">Quick Response:</span> I typically reply within 24 hours
              </p>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
