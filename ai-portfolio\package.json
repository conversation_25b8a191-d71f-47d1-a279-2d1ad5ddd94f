{"name": "ai-portfolio", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "export": "next build && next export", "deploy": "bash deploy.sh"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@react-three/drei": "^10.3.0", "@react-three/fiber": "^9.1.2", "@types/three": "^0.177.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.18.1", "gsap": "^3.13.0", "lucide-react": "^0.517.0", "next": "15.3.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.58.1", "react-intersection-observer": "^9.16.0", "tailwind-merge": "^3.3.1", "three": "^0.177.0", "web-vitals": "^5.0.3", "zod": "^3.25.67"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19.1.6", "eslint-config-prettier": "^10.1.5", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.12", "tailwindcss": "^4", "typescript": "^5"}}