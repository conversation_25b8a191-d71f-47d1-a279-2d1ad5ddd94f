# AI Portfolio - SANJAI S

A stunning, AI-focused portfolio website showcasing expertise as a Junior Software Developer and AI & Data Science student. Built with cutting-edge technologies and featuring 3D animations, interactive elements, and a futuristic design.

## 🚀 Features

- **AI-Inspired Design**: Futuristic theme with neural network animations and glassmorphism effects
- **3D Animations**: Interactive Three.js background with particle systems
- **Responsive Design**: Optimized for all devices and screen sizes
- **Performance Optimized**: Lighthouse scores above 90, lazy loading, and code splitting
- **Modern Tech Stack**: Next.js 15, TypeScript, Tailwind CSS, Framer Motion
- **Interactive Elements**: Smooth animations, hover effects, and scroll-triggered animations
- **Contact Form**: Functional contact form with validation and AI-chatbot styling
- **Dark/Light Theme**: Toggle between themes with smooth transitions

## 🛠️ Tech Stack

### Frontend
- **Next.js 15** - React framework with App Router
- **TypeScript** - Type-safe development
- **Tailwind CSS** - Utility-first CSS framework
- **Framer Motion** - Animation library
- **Three.js** - 3D graphics and animations
- **GSAP** - Advanced animations

### UI Components
- **Lucide React** - Beautiful icons
- **React Hook Form** - Form handling
- **Zod** - Schema validation
- **Class Variance Authority** - Component variants

### Performance
- **Dynamic Imports** - Code splitting
- **Lazy Loading** - Image and component optimization
- **Web Vitals** - Performance monitoring
- **Service Worker** - Caching strategies

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/ai-portfolio.git
   cd ai-portfolio
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Run the development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

### Build for Production

```bash
npm run build
npm start
```

## 📱 Sections

### 🏠 Hero Section
- Animated 3D neural network background
- Typing animations with role rotation
- Social media links
- Call-to-action buttons

### 👨‍💻 About Me
- Personal information and photo
- Animated skill bars with percentages
- Circular skill indicators
- Certification grid with status tracking

### 💼 Experience
- Timeline-based layout
- Internship and project details
- Technology tags
- Achievement highlights

### 🚀 Projects
- Filterable project grid
- 3D hover effects
- Technology filtering
- Live demo and GitHub links

### 📞 Contact
- Interactive contact form
- Form validation with Zod
- Contact information cards
- Social media integration

## ⚡ Performance Features

- **Lighthouse Score**: 90+ across all metrics
- **Code Splitting**: Automatic route-based splitting
- **Lazy Loading**: Images and 3D components
- **Optimized Images**: WebP/AVIF formats
- **Caching**: Service worker implementation
- **Bundle Analysis**: Webpack optimization

## 🚀 Deployment

### Vercel (Recommended)
1. Push your code to GitHub
2. Connect your repository to Vercel
3. Deploy automatically with each push

### Manual Deployment
```bash
npm run build
npm run export  # For static export if needed
```

## 📞 Contact

**SANJAI S** - Junior Software Developer & AI Student (Class of 2025)
- Email: <EMAIL>
- Phone: +91 8072686247
- LinkedIn: [SANJAI S](https://linkedin.com/in/sanjai-s-ai)
- GitHub: [sanjai827054](https://github.com/sanjai827054)

---

⭐ **Star this repository if you found it helpful!**
