"use client"

import { useEffect, useRef, useState } from 'react'
import { motion, useInView } from 'framer-motion'
import { cn } from '@/lib/utils'

interface SkillBarProps {
  name: string
  percentage: number
  icon?: React.ReactNode
  color?: 'blue' | 'purple' | 'green' | 'pink' | 'orange'
  delay?: number
  className?: string
}

const colorVariants = {
  blue: {
    bg: 'from-cyber-blue to-hologram-blue',
    glow: 'shadow-[0_0_20px_rgba(0,212,255,0.3)]'
  },
  purple: {
    bg: 'from-electric-violet to-quantum-purple',
    glow: 'shadow-[0_0_20px_rgba(139,92,246,0.3)]'
  },
  green: {
    bg: 'from-neon-green to-matrix-green',
    glow: 'shadow-[0_0_20px_rgba(0,255,136,0.3)]'
  },
  pink: {
    bg: 'from-plasma-pink to-red-500',
    glow: 'shadow-[0_0_20px_rgba(255,0,110,0.3)]'
  },
  orange: {
    bg: 'from-orange-500 to-yellow-500',
    glow: 'shadow-[0_0_20px_rgba(249,115,22,0.3)]'
  }
}

export function SkillBar({ 
  name, 
  percentage, 
  icon, 
  color = 'blue', 
  delay = 0,
  className 
}: SkillBarProps) {
  const ref = useRef<HTMLDivElement>(null)
  const isInView = useInView(ref, { once: true, margin: "-100px" })
  const [animatedPercentage, setAnimatedPercentage] = useState(0)

  useEffect(() => {
    if (isInView) {
      const timer = setTimeout(() => {
        setAnimatedPercentage(percentage)
      }, delay)
      return () => clearTimeout(timer)
    }
  }, [isInView, percentage, delay])

  const colorConfig = colorVariants[color]

  return (
    <motion.div
      ref={ref}
      className={cn("space-y-2", className)}
      initial={{ opacity: 0, y: 20 }}
      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
      transition={{ duration: 0.6, delay: delay / 1000 }}
    >
      {/* Skill Name and Percentage */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          {icon && <span className="text-foreground/80">{icon}</span>}
          <span className="text-sm font-medium text-foreground">{name}</span>
        </div>
        <motion.span 
          className="text-sm font-bold text-primary"
          initial={{ opacity: 0 }}
          animate={isInView ? { opacity: 1 } : { opacity: 0 }}
          transition={{ duration: 0.3, delay: (delay + 500) / 1000 }}
        >
          {animatedPercentage}%
        </motion.span>
      </div>

      {/* Progress Bar Background */}
      <div className="relative h-2 bg-foreground/10 rounded-full overflow-hidden">
        {/* Animated Progress Bar */}
        <motion.div
          className={cn(
            "h-full bg-gradient-to-r rounded-full relative",
            colorConfig.bg
          )}
          initial={{ width: 0 }}
          animate={isInView ? { width: `${percentage}%` } : { width: 0 }}
          transition={{ 
            duration: 1.5, 
            delay: (delay + 200) / 1000,
            ease: "easeOut"
          }}
        >
          {/* Glow Effect */}
          <div className={cn(
            "absolute inset-0 rounded-full opacity-60",
            colorConfig.glow
          )} />
          
          {/* Shimmer Effect */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -skew-x-12 animate-shimmer" />
        </motion.div>

        {/* Pulse Effect on Hover */}
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300" />
      </div>
    </motion.div>
  )
}

interface SkillCategoryProps {
  title: string
  skills: Array<{
    name: string
    percentage: number
    icon?: React.ReactNode
    color?: 'blue' | 'purple' | 'green' | 'pink' | 'orange'
  }>
  className?: string
}

export function SkillCategory({ title, skills, className }: SkillCategoryProps) {
  const ref = useRef<HTMLDivElement>(null)
  const isInView = useInView(ref, { once: true, margin: "-50px" })

  return (
    <motion.div
      ref={ref}
      className={cn("space-y-4", className)}
      initial={{ opacity: 0, y: 30 }}
      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
      transition={{ duration: 0.6 }}
    >
      <h3 className="text-xl font-semibold text-foreground mb-4 flex items-center">
        <span className="w-2 h-2 bg-primary rounded-full mr-3" />
        {title}
      </h3>
      
      <div className="space-y-4">
        {skills.map((skill, index) => (
          <SkillBar
            key={skill.name}
            name={skill.name}
            percentage={skill.percentage}
            icon={skill.icon}
            color={skill.color}
            delay={index * 200}
          />
        ))}
      </div>
    </motion.div>
  )
}

// Circular Skill Component
interface CircularSkillProps {
  name: string
  percentage: number
  size?: number
  strokeWidth?: number
  color?: string
  delay?: number
}

export function CircularSkill({ 
  name, 
  percentage, 
  size = 120, 
  strokeWidth = 8,
  color = '#00d4ff',
  delay = 0 
}: CircularSkillProps) {
  const ref = useRef<HTMLDivElement>(null)
  const isInView = useInView(ref, { once: true })
  const [animatedPercentage, setAnimatedPercentage] = useState(0)

  const radius = (size - strokeWidth) / 2
  const circumference = radius * 2 * Math.PI
  const strokeDashoffset = circumference - (animatedPercentage / 100) * circumference

  useEffect(() => {
    if (isInView) {
      const timer = setTimeout(() => {
        setAnimatedPercentage(percentage)
      }, delay)
      return () => clearTimeout(timer)
    }
  }, [isInView, percentage, delay])

  return (
    <motion.div
      ref={ref}
      className="flex flex-col items-center space-y-2"
      initial={{ opacity: 0, scale: 0.8 }}
      animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.8 }}
      transition={{ duration: 0.6, delay: delay / 1000 }}
    >
      <div className="relative" style={{ width: size, height: size }}>
        <svg
          className="transform -rotate-90"
          width={size}
          height={size}
        >
          {/* Background Circle */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke="currentColor"
            strokeWidth={strokeWidth}
            fill="none"
            className="text-foreground/10"
          />
          
          {/* Progress Circle */}
          <motion.circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke={color}
            strokeWidth={strokeWidth}
            fill="none"
            strokeLinecap="round"
            strokeDasharray={circumference}
            strokeDashoffset={strokeDashoffset}
            initial={{ strokeDashoffset: circumference }}
            animate={isInView ? { strokeDashoffset } : { strokeDashoffset: circumference }}
            transition={{ duration: 1.5, delay: delay / 1000, ease: "easeOut" }}
            style={{ filter: `drop-shadow(0 0 8px ${color}40)` }}
          />
        </svg>
        
        {/* Percentage Text */}
        <div className="absolute inset-0 flex items-center justify-center">
          <motion.span 
            className="text-lg font-bold text-foreground"
            initial={{ opacity: 0 }}
            animate={isInView ? { opacity: 1 } : { opacity: 0 }}
            transition={{ duration: 0.3, delay: (delay + 500) / 1000 }}
          >
            {animatedPercentage}%
          </motion.span>
        </div>
      </div>
      
      <span className="text-sm font-medium text-foreground/80 text-center">{name}</span>
    </motion.div>
  )
}
