"use client"

import { useRef } from 'react'
import { motion, useInView } from 'framer-motion'
import { Calendar, MapPin, ExternalLink, ChevronRight } from 'lucide-react'
import { Card, CardContent, NeuralCard } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'

interface TimelineItemProps {
  title: string
  company: string
  location: string
  duration: string
  type: 'internship' | 'project' | 'education' | 'certification'
  description: string
  achievements: string[]
  technologies: string[]
  link?: string
  isLeft?: boolean
  delay?: number
  className?: string
}

const typeConfig = {
  internship: {
    color: 'from-cyber-blue to-hologram-blue',
    icon: '💼',
    label: 'Internship'
  },
  project: {
    color: 'from-electric-violet to-quantum-purple',
    icon: '🚀',
    label: 'Project'
  },
  education: {
    color: 'from-neon-green to-matrix-green',
    icon: '🎓',
    label: 'Education'
  },
  certification: {
    color: 'from-plasma-pink to-red-500',
    icon: '🏆',
    label: 'Certification'
  }
}

export function TimelineItem({
  title,
  company,
  location,
  duration,
  type,
  description,
  achievements,
  technologies,
  link,
  isLeft = false,
  delay = 0,
  className
}: TimelineItemProps) {
  const ref = useRef<HTMLDivElement>(null)
  const isInView = useInView(ref, { once: true, margin: "-100px" })
  
  const config = typeConfig[type]

  return (
    <motion.div
      ref={ref}
      className={cn(
        "relative flex items-center",
        isLeft ? "md:flex-row-reverse" : "md:flex-row",
        className
      )}
      initial={{ opacity: 0, x: isLeft ? 50 : -50 }}
      animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: isLeft ? 50 : -50 }}
      transition={{ duration: 0.6, delay: delay / 1000 }}
    >
      {/* Timeline Line & Node */}
      <div className="hidden md:flex flex-col items-center absolute left-1/2 transform -translate-x-1/2 z-10">
        {/* Timeline Node */}
        <motion.div
          className={cn(
            "w-4 h-4 rounded-full bg-gradient-to-r border-4 border-background shadow-lg",
            config.color
          )}
          initial={{ scale: 0 }}
          animate={isInView ? { scale: 1 } : { scale: 0 }}
          transition={{ duration: 0.3, delay: (delay + 200) / 1000 }}
        />
      </div>

      {/* Content Card */}
      <div className={cn(
        "w-full md:w-5/12",
        isLeft ? "md:pr-8" : "md:pl-8"
      )}>
        <NeuralCard className="group hover:scale-[1.02] transition-all duration-300">
          <CardContent className="p-6">
            {/* Header */}
            <div className="flex items-start justify-between mb-4">
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-2">
                  <span className="text-lg">{config.icon}</span>
                  <span className={cn(
                    "px-2 py-1 text-xs font-medium rounded-full bg-gradient-to-r text-white",
                    config.color
                  )}>
                    {config.label}
                  </span>
                </div>
                
                <h3 className="text-xl font-bold text-foreground group-hover:text-primary transition-colors">
                  {title}
                </h3>
                <p className="text-lg font-medium text-primary mb-1">{company}</p>
                
                <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-4 text-sm text-foreground/70">
                  <div className="flex items-center space-x-1">
                    <Calendar className="w-4 h-4" />
                    <span>{duration}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <MapPin className="w-4 h-4" />
                    <span>{location}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Description */}
            <p className="text-foreground/80 mb-4 leading-relaxed">
              {description}
            </p>

            {/* Achievements */}
            {achievements.length > 0 && (
              <div className="mb-4">
                <h4 className="text-sm font-semibold text-foreground mb-2">Key Achievements:</h4>
                <ul className="space-y-1">
                  {achievements.map((achievement, index) => (
                    <motion.li
                      key={index}
                      className="flex items-start space-x-2 text-sm text-foreground/80"
                      initial={{ opacity: 0, x: -10 }}
                      animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -10 }}
                      transition={{ duration: 0.3, delay: (delay + 300 + index * 100) / 1000 }}
                    >
                      <ChevronRight className="w-3 h-3 text-primary mt-0.5 flex-shrink-0" />
                      <span>{achievement}</span>
                    </motion.li>
                  ))}
                </ul>
              </div>
            )}

            {/* Technologies */}
            <div className="mb-4">
              <h4 className="text-sm font-semibold text-foreground mb-2">Technologies Used:</h4>
              <div className="flex flex-wrap gap-2">
                {technologies.map((tech, index) => (
                  <motion.span
                    key={tech}
                    className="px-2 py-1 text-xs bg-primary/10 text-primary rounded-md border border-primary/20"
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.8 }}
                    transition={{ duration: 0.3, delay: (delay + 400 + index * 50) / 1000 }}
                  >
                    {tech}
                  </motion.span>
                ))}
              </div>
            </div>

            {/* Action Button */}
            {link && (
              <Button
                variant="outline"
                size="sm"
                className="group/btn"
                asChild
              >
                <a href={link} target="_blank" rel="noopener noreferrer">
                  <span>Learn More</span>
                  <ExternalLink className="w-3 h-3 ml-2 group-hover/btn:translate-x-1 transition-transform" />
                </a>
              </Button>
            )}
          </CardContent>
        </NeuralCard>
      </div>

      {/* Mobile Timeline Indicator */}
      <div className="md:hidden absolute left-4 top-6">
        <div className={cn(
          "w-3 h-3 rounded-full bg-gradient-to-r",
          config.color
        )} />
        <div className="w-0.5 bg-gradient-to-b from-primary/50 to-transparent h-full absolute left-1/2 transform -translate-x-1/2 top-3" />
      </div>
    </motion.div>
  )
}

interface TimelineProps {
  items: Array<{
    title: string
    company: string
    location: string
    duration: string
    type: 'internship' | 'project' | 'education' | 'certification'
    description: string
    achievements: string[]
    technologies: string[]
    link?: string
  }>
  className?: string
}

export function Timeline({ items, className }: TimelineProps) {
  return (
    <div className={cn("relative", className)}>
      {/* Desktop Timeline Line */}
      <div className="hidden md:block absolute left-1/2 transform -translate-x-1/2 w-0.5 bg-gradient-to-b from-primary/50 via-primary/30 to-primary/50 h-full" />
      
      {/* Mobile Timeline Line */}
      <div className="md:hidden absolute left-4 w-0.5 bg-gradient-to-b from-primary/50 via-primary/30 to-primary/50 h-full" />

      <div className="space-y-12 md:space-y-16">
        {items.map((item, index) => (
          <TimelineItem
            key={`${item.title}-${item.company}`}
            {...item}
            isLeft={index % 2 === 1}
            delay={index * 200}
            className="md:pl-0 pl-12"
          />
        ))}
      </div>
    </div>
  )
}
