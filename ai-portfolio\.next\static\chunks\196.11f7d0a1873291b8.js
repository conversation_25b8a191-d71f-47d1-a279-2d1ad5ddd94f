"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[196],{815:(e,t,r)=>{r.r(t),r.d(t,{default:()=>f});var n=r(5155),a=r(2115),i=r(3816),s=r(7558),o=r(5830),l=r(9107),c=r(3264);function u(){let e=(0,a.useRef)(null),[t,r]=(0,a.useMemo)(()=>{let e=new Float32Array(900),t=[];for(let t=0;t<300;t++)e[3*t]=(Math.random()-.5)*10,e[3*t+1]=(Math.random()-.5)*10,e[3*t+2]=(Math.random()-.5)*10;for(let r=0;r<300;r++){let n=new c.Pq0(e[3*r],e[3*r+1],e[3*r+2]);for(let a=r+1;a<300;a++){let i=new c.Pq0(e[3*a],e[3*a+1],e[3*a+2]);2>n.distanceTo(i)&&t.push([r,a])}}return[e,t]},[]);return(0,i.C)(t=>{e.current&&(e.current.rotation.x=.1*Math.sin(.1*t.clock.elapsedTime),e.current.rotation.y=.1*Math.sin(.15*t.clock.elapsedTime),e.current.rotation.z=.05*Math.sin(.05*t.clock.elapsedTime))}),(0,n.jsxs)("group",{ref:e,children:[(0,n.jsx)(o.ON,{positions:t,children:(0,n.jsx)(l.q,{transparent:!0,color:"#00d4ff",size:.05,sizeAttenuation:!0,depthWrite:!1,opacity:.8})}),r.map((e,r)=>{let[a,i]=e;return(0,n.jsxs)("line",{children:[(0,n.jsx)("bufferGeometry",{children:(0,n.jsx)("bufferAttribute",{attach:"attributes-position",count:2,array:new Float32Array([t[3*a],t[3*a+1],t[3*a+2],t[3*i],t[3*i+1],t[3*i+2]]),itemSize:3})}),(0,n.jsx)("lineBasicMaterial",{color:"#8b5cf6",transparent:!0,opacity:.3})]},r)})]})}function d(){let e=(0,a.useRef)(null),t=(0,a.useMemo)(()=>{let e=new Float32Array(3e3);for(let t=0;t<1e3;t++)e[3*t]=(Math.random()-.5)*20,e[3*t+1]=(Math.random()-.5)*20,e[3*t+2]=(Math.random()-.5)*20;return e},[]);return(0,i.C)(t=>{e.current&&(e.current.rotation.x=.02*t.clock.elapsedTime,e.current.rotation.y=.03*t.clock.elapsedTime)}),(0,n.jsx)(o.ON,{ref:e,positions:t,children:(0,n.jsx)(l.q,{transparent:!0,color:"#00ff88",size:.02,sizeAttenuation:!0,depthWrite:!1,opacity:.6})})}function p(){return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("ambientLight",{intensity:.5}),(0,n.jsx)("pointLight",{position:[10,10,10]}),(0,n.jsx)(u,{}),(0,n.jsx)(d,{})]})}function f(){return(0,n.jsx)("div",{className:"absolute inset-0 -z-10",children:(0,n.jsx)(s.Hl,{camera:{position:[0,0,5],fov:75},style:{background:"transparent"},dpr:[1,2],performance:{min:.5},children:(0,n.jsx)(a.Suspense,{fallback:null,children:(0,n.jsx)(p,{})})})})}}}]);