(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{285:(e,t,a)=>{"use strict";a.d(t,{$:()=>l});var r=a(5155),i=a(2115),s=a(2085),n=a(9434);let o=(0,s.F)("inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary disabled:pointer-events-none disabled:opacity-50 relative overflow-hidden group",{variants:{variant:{default:"glass text-foreground hover:glow-blue hover:scale-105",primary:"bg-gradient-to-r from-cyber-blue to-hologram-blue text-white hover:shadow-lg hover:scale-105",secondary:"bg-gradient-to-r from-electric-violet to-quantum-purple text-white hover:shadow-lg hover:scale-105",accent:"bg-gradient-to-r from-neon-green to-matrix-green text-neural-black hover:shadow-lg hover:scale-105",outline:"border-2 border-primary/50 text-primary hover:bg-primary/10 hover:border-primary hover:glow-blue",ghost:"text-foreground hover:bg-primary/10 hover:text-primary",destructive:"bg-gradient-to-r from-red-500 to-red-600 text-white hover:shadow-lg hover:scale-105",glow:"glass text-foreground hover:glow-purple hover:scale-105 border border-electric-violet/30"},size:{default:"h-10 px-4 py-2",sm:"h-8 px-3 text-xs",lg:"h-12 px-8 text-base",xl:"h-14 px-10 text-lg",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=i.forwardRef((e,t)=>{let{className:a,variant:i,size:s,asChild:l=!1,children:c,...d}=e;return(0,r.jsxs)("button",{className:(0,n.cn)(o({variant:i,size:s,className:a})),ref:t,...d,children:[("primary"===i||"secondary"===i||"accent"===i)&&(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700"}),(0,r.jsx)("div",{className:"absolute inset-0 rounded-lg opacity-0 group-active:opacity-100 bg-white/20 transition-opacity duration-150"}),(0,r.jsx)("span",{className:"relative z-10",children:c})]})});l.displayName="Button"},848:(e,t,a)=>{"use strict";a.d(t,{default:()=>v});var r=a(5155),i=a(2115),s=a(4407),n=a(9099),o=a(2894),l=a(8883),c=a(1788),d=a(6474),m=a(285),p=a(9434);function u(e){let{text:t,className:a,speed:s=100,delay:n=0,repeat:o=!1,cursor:l=!0,onComplete:c}=e,[d,m]=(0,i.useState)(""),[u,x]=(0,i.useState)(0),[h,g]=(0,i.useState)(0),[f,b]=(0,i.useState)(!1),[v,y]=(0,i.useState)(!0),j=Array.isArray(t)?t:[t],N=j[h];return(0,i.useEffect)(()=>{let e=setTimeout(()=>{f?u>0?(m(N.slice(0,u-1)),x(u-1)):(b(!1),g(e=>(e+1)%j.length)):u<N.length?(m(N.slice(0,u+1)),x(u+1)):j.length>1?setTimeout(()=>b(!0),2e3):null==c||c()},n>0?n:f?s/2:s);return n>0&&(n=0),()=>clearTimeout(e)},[u,N,f,s,n,j,h,c]),(0,i.useEffect)(()=>{if(!l)return;let e=setInterval(()=>{y(e=>!e)},530);return()=>clearInterval(e)},[l]),(0,r.jsxs)("span",{className:(0,p.cn)("inline-block",a),children:[d,l&&(0,r.jsx)("span",{className:(0,p.cn)("inline-block w-0.5 h-[1em] bg-current ml-1 transition-opacity duration-100",v?"opacity-100":"opacity-0")})]})}function x(e){let{text:t,className:a,speed:i=100,delay:s=0,gradientColors:n=["#00d4ff","#8b5cf6","#00ff88"],...o}=e;return(0,r.jsx)(u,{text:t,className:(0,p.cn)("gradient-text",a),speed:i,delay:s,...o})}var h=a(5548);let g=(0,a(5028).default)(()=>Promise.all([a.e(96),a.e(196)]).then(a.bind(a,815)),{loadableGenerated:{webpack:()=>[815]},ssr:!1,loading:()=>(0,r.jsx)("div",{className:"absolute inset-0 -z-10 bg-gradient-to-br from-neural-black/80 via-deep-space/60 to-neural-black/80"})}),f=[{icon:n.A,href:"https://github.com/sanjai827054",label:"GitHub"},{icon:o.A,href:"https://linkedin.com/in/sanjai-s-ai",label:"LinkedIn"},{icon:l.A,href:"mailto:<EMAIL>",label:"Email"}],b=["Full Stack Developer","Java Backend Developer","Frontend Web Developer","Bootstrap & JavaScript Expert","HTML/CSS Specialist"];function v(){let e=(0,i.useRef)(null),t=(0,i.useRef)(null),a=(0,i.useRef)(null),n=(0,i.useRef)(null),o=(0,i.useRef)(null);(0,i.useEffect)(()=>{let r=h.os.context(()=>{h.os.set([t.current,a.current,n.current,o.current],{opacity:0,y:50}),h.os.timeline({delay:.5}).to(t.current,{opacity:1,y:0,duration:1,ease:"power3.out"}).to(a.current,{opacity:1,y:0,duration:1,ease:"power3.out"},"-=0.5").to(n.current,{opacity:1,y:0,duration:1,ease:"power3.out"},"-=0.5").to(o.current,{opacity:1,y:0,duration:1,ease:"power3.out"},"-=0.5")},e);return()=>r.revert()},[]);let l=()=>{let e=document.getElementById("about");null==e||e.scrollIntoView({behavior:"smooth"})};return(0,r.jsxs)("section",{ref:e,className:"relative min-h-screen flex items-center justify-center overflow-hidden neural-bg",children:[(0,r.jsx)(i.Suspense,{fallback:(0,r.jsx)("div",{className:"absolute inset-0 -z-10 bg-gradient-to-br from-neural-black/80 via-deep-space/60 to-neural-black/80"}),children:(0,r.jsx)(g,{})}),(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-neural-black/80 via-deep-space/60 to-neural-black/80"}),(0,r.jsxs)("div",{className:"relative z-10 max-w-6xl mx-auto px-6 text-center",children:[(0,r.jsxs)(s.P.h1,{ref:t,className:"text-5xl md:text-7xl lg:text-8xl font-bold mb-6",initial:{opacity:0,y:50},animate:{opacity:1,y:0},transition:{duration:1,delay:.5},children:[(0,r.jsx)("span",{className:"block text-foreground mb-2",children:"Hello, I'm"}),(0,r.jsx)("span",{className:"block gradient-text",children:(0,r.jsx)(u,{text:"SANJAI S",speed:150,delay:1500})})]}),(0,r.jsxs)("div",{ref:a,className:"mb-8",children:[(0,r.jsx)("div",{className:"text-xl md:text-2xl lg:text-3xl text-foreground/80 mb-4",children:(0,r.jsx)(x,{text:b,speed:100,delay:3e3,repeat:!0})}),(0,r.jsx)("p",{className:"text-lg md:text-xl text-foreground/70 max-w-3xl mx-auto leading-relaxed",children:"Passionate Full Stack Developer specializing in Java backend development and modern frontend technologies. Expert in HTML, CSS, JavaScript, Bootstrap, and Java. Building responsive, scalable web applications with clean code and innovative solutions."})]}),(0,r.jsxs)("div",{ref:n,className:"flex flex-col sm:flex-row gap-4 justify-center items-center mb-12",children:[(0,r.jsxs)(m.$,{size:"lg",variant:"primary",className:"group",children:[(0,r.jsx)(c.A,{className:"w-5 h-5 mr-2 group-hover:animate-bounce"}),"Download Resume"]}),(0,r.jsxs)(m.$,{size:"lg",variant:"outline",onClick:l,children:["View My Work",(0,r.jsx)(d.A,{className:"w-5 h-5 ml-2 animate-bounce"})]})]}),(0,r.jsx)("div",{ref:o,className:"flex justify-center space-x-6",children:f.map(e=>{let{icon:t,href:a,label:i}=e;return(0,r.jsx)(s.P.a,{href:a,className:"p-3 glass rounded-full hover:glow-blue transition-all duration-300 group",whileHover:{scale:1.1},whileTap:{scale:.95},"aria-label":i,children:(0,r.jsx)(t,{className:"w-6 h-6 text-foreground group-hover:text-primary transition-colors"})},i)})}),(0,r.jsx)("div",{className:"absolute top-20 left-10 w-20 h-20 rounded-full bg-cyber-blue/10 animate-pulse"}),(0,r.jsx)("div",{className:"absolute top-40 right-20 w-16 h-16 rounded-full bg-electric-violet/10 animate-pulse delay-1000"}),(0,r.jsx)("div",{className:"absolute bottom-40 left-20 w-12 h-12 rounded-full bg-neon-green/10 animate-pulse delay-2000"})]}),(0,r.jsx)(s.P.div,{className:"absolute bottom-8 left-1/2 transform -translate-x-1/2",animate:{y:[0,10,0]},transition:{duration:2,repeat:1/0},children:(0,r.jsxs)("button",{onClick:l,className:"flex flex-col items-center text-foreground/60 hover:text-primary transition-colors group",children:[(0,r.jsx)("span",{className:"text-sm mb-2 group-hover:text-primary",children:"Scroll Down"}),(0,r.jsx)(d.A,{className:"w-6 h-6 animate-bounce"})]})}),(0,r.jsx)("div",{className:"absolute inset-0 opacity-5",children:(0,r.jsx)("div",{className:"absolute inset-0",style:{backgroundImage:"\n            linear-gradient(rgba(0, 212, 255, 0.1) 1px, transparent 1px),\n            linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1px, transparent 1px)\n          ",backgroundSize:"50px 50px"}})})]})}},1483:(e,t,a)=>{"use strict";a.d(t,{ThemeProvider:()=>n,U:()=>o});var r=a(5155),i=a(2115);let s=(0,i.createContext)(void 0);function n(e){let{children:t,defaultTheme:a="dark"}=e,[n,o]=(0,i.useState)(a),[l,c]=(0,i.useState)(!1);return((0,i.useEffect)(()=>{let e=localStorage.getItem("theme"),t=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";o(e||t||a),c(!0)},[a]),(0,i.useEffect)(()=>{if(!l)return;let e=document.documentElement;e.removeAttribute("data-theme"),"light"===n&&e.setAttribute("data-theme","light"),localStorage.setItem("theme",n)},[n,l]),l)?(0,r.jsx)(s.Provider,{value:{theme:n,toggleTheme:()=>{o(e=>"dark"===e?"light":"dark")},setTheme:e=>{o(e)}},children:t}):(0,r.jsx)("div",{style:{visibility:"hidden"},children:t})}function o(){let{theme:e,toggleTheme:t}=function(){let e=(0,i.useContext)(s);if(void 0===e)throw Error("useTheme must be used within a ThemeProvider");return e}();return(0,r.jsxs)("button",{onClick:t,className:"relative p-2 rounded-lg glass hover:glow-blue transition-all duration-300 group","aria-label":"Toggle theme",children:[(0,r.jsxs)("div",{className:"relative w-6 h-6",children:[(0,r.jsx)("svg",{className:"absolute inset-0 w-6 h-6 transition-all duration-300 ".concat("light"===e?"rotate-0 scale-100 opacity-100":"rotate-90 scale-0 opacity-0"),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"})}),(0,r.jsx)("svg",{className:"absolute inset-0 w-6 h-6 transition-all duration-300 ".concat("dark"===e?"rotate-0 scale-100 opacity-100":"-rotate-90 scale-0 opacity-0"),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"})})]}),(0,r.jsx)("div",{className:"absolute inset-0 rounded-lg bg-gradient-to-r from-cyber-blue/20 to-electric-violet/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"})]})}},1762:(e,t,a)=>{"use strict";a.d(t,{default:()=>L});var r=a(5155),i=a(2115),s=a(6604),n=a(4407),o=a(760),l=a(9099),c=a(3786),d=a(5690),m=a(9074),p=a(7580),u=a(2657),x=a(6695),h=a(285),g=a(9434);let f={completed:{color:"text-neon-green",bg:"bg-neon-green/10",border:"border-neon-green/30",label:"Completed"},"in-progress":{color:"text-cyber-blue",bg:"bg-cyber-blue/10",border:"border-cyber-blue/30",label:"In Progress"},planned:{color:"text-electric-violet",bg:"bg-electric-violet/10",border:"border-electric-violet/30",label:"Planned"}};function b(e){let{title:t,description:a,longDescription:o,image:b,technologies:v,category:y,githubUrl:j,liveUrl:N,demoUrl:w,date:S,team:k,status:C,featured:P=!1,delay:A=0,onViewDetails:B,className:J}=e,[T,L]=(0,i.useState)(!1),M=(0,i.useRef)(null),I=(0,s.W)(M,{once:!0,margin:"-100px"}),D=f[C];return(0,r.jsx)(n.P.div,{ref:M,className:(0,g.cn)("group relative",P&&"md:col-span-2 md:row-span-2",J),initial:{opacity:0,y:50},animate:I?{opacity:1,y:0}:{opacity:0,y:50},transition:{duration:.6,delay:A/1e3},onMouseEnter:()=>L(!0),onMouseLeave:()=>L(!1),children:(0,r.jsxs)(x.Zp,{variant:"neural",className:(0,g.cn)("h-full overflow-hidden transition-all duration-500 cursor-pointer","hover:scale-[1.02] hover:-translate-y-2 hover:shadow-2xl",P&&"hover:scale-[1.01]",D.border),onClick:B,children:[(0,r.jsx)("div",{className:"relative overflow-hidden",children:(0,r.jsxs)("div",{className:(0,g.cn)("aspect-video bg-gradient-to-br from-cyber-blue/20 to-electric-violet/20 relative",P&&"aspect-[16/10]"),children:[b?(0,r.jsx)("img",{src:b,alt:t,className:"absolute inset-0 w-full h-full object-cover"}):(0,r.jsx)("div",{className:"absolute inset-0 flex items-center justify-center text-6xl opacity-50",children:"\uD83D\uDE80"}),(0,r.jsx)("div",{className:(0,g.cn)("absolute top-4 left-4 px-2 py-1 rounded-full text-xs font-medium",D.bg,D.color),children:D.label}),(0,r.jsx)("div",{className:"absolute top-4 right-4 px-2 py-1 rounded-full bg-background/80 backdrop-blur-sm text-xs font-medium text-foreground",children:y}),(0,r.jsx)(n.P.div,{className:"absolute inset-0 bg-gradient-to-t from-neural-black/80 via-transparent to-transparent flex items-end justify-center pb-6",initial:{opacity:0},animate:{opacity:+!!T},transition:{duration:.3},children:(0,r.jsxs)("div",{className:"flex space-x-2",children:[j&&(0,r.jsx)(h.$,{size:"sm",variant:"ghost",className:"bg-background/20 backdrop-blur-sm hover:bg-background/40",onClick:e=>{e.stopPropagation(),window.open(j,"_blank")},children:(0,r.jsx)(l.A,{className:"w-4 h-4"})}),N&&(0,r.jsx)(h.$,{size:"sm",variant:"ghost",className:"bg-background/20 backdrop-blur-sm hover:bg-background/40",onClick:e=>{e.stopPropagation(),window.open(N,"_blank")},children:(0,r.jsx)(c.A,{className:"w-4 h-4"})}),w&&(0,r.jsx)(h.$,{size:"sm",variant:"ghost",className:"bg-background/20 backdrop-blur-sm hover:bg-background/40",onClick:e=>{e.stopPropagation(),window.open(w,"_blank")},children:(0,r.jsx)(d.A,{className:"w-4 h-4"})})]})})]})}),(0,r.jsxs)(x.Wu,{className:"p-6",children:[(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("h3",{className:(0,g.cn)("font-bold text-foreground group-hover:text-primary transition-colors mb-2",P?"text-2xl":"text-xl"),children:t}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-foreground/60 mb-3",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(m.A,{className:"w-3 h-3"}),(0,r.jsx)("span",{children:S})]}),k&&(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(p.A,{className:"w-3 h-3"}),(0,r.jsx)("span",{children:k})]})]})]}),(0,r.jsx)("p",{className:(0,g.cn)("text-foreground/80 mb-4 leading-relaxed",P?"text-base":"text-sm"),children:a}),(0,r.jsx)("div",{className:"mb-4",children:(0,r.jsxs)("div",{className:"flex flex-wrap gap-2",children:[v.slice(0,P?8:5).map((e,t)=>(0,r.jsx)(n.P.span,{className:"px-2 py-1 text-xs bg-primary/10 text-primary rounded-md border border-primary/20",initial:{opacity:0,scale:.8},animate:I?{opacity:1,scale:1}:{opacity:0,scale:.8},transition:{duration:.3,delay:(A+200+50*t)/1e3},children:e},e)),v.length>(P?8:5)&&(0,r.jsxs)("span",{className:"px-2 py-1 text-xs text-foreground/60 rounded-md",children:["+",v.length-(P?8:5)," more"]})]})}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)(h.$,{variant:"outline",size:"sm",className:"group/btn",onClick:e=>{e.stopPropagation(),null==B||B()},children:[(0,r.jsx)(u.A,{className:"w-3 h-3 mr-2"}),(0,r.jsx)("span",{children:"View Details"})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[j&&(0,r.jsx)(h.$,{variant:"ghost",size:"icon",className:"w-8 h-8",onClick:e=>{e.stopPropagation(),window.open(j,"_blank")},children:(0,r.jsx)(l.A,{className:"w-4 h-4"})}),N&&(0,r.jsx)(h.$,{variant:"ghost",size:"icon",className:"w-8 h-8",onClick:e=>{e.stopPropagation(),window.open(N,"_blank")},children:(0,r.jsx)(c.A,{className:"w-4 h-4"})})]})]})]}),P&&(0,r.jsx)("div",{className:"absolute top-6 left-6 px-3 py-1 bg-gradient-to-r from-cyber-blue to-electric-violet text-white text-xs font-bold rounded-full",children:"Featured"}),(0,r.jsx)("div",{className:"absolute inset-0 rounded-xl bg-gradient-to-r from-primary/10 via-transparent to-secondary/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"})]})})}var v=a(4869),y=a(9376),j=a(9621),N=a(6767),w=a(4213),S=a(3314);let k={all:v.A,ai:y.A,web:j.A,mobile:N.A,data:w.A,system:S.A};function C(e){let{categories:t,activeFilter:a,onFilterChange:i,className:s}=e;return(0,r.jsx)("div",{className:(0,g.cn)("flex flex-wrap gap-3 justify-center",s),children:t.map((e,t)=>{let s=k[e.id]||v.A,o=a===e.id;return(0,r.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.1*t},children:(0,r.jsxs)(h.$,{variant:o?"primary":"outline",size:"sm",onClick:()=>i(e.id),className:(0,g.cn)("group relative overflow-hidden transition-all duration-300",o?"shadow-lg scale-105":"hover:scale-105 hover:shadow-md"),children:[!o&&(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-primary/10 to-secondary/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),(0,r.jsxs)("div",{className:"relative flex items-center space-x-2",children:[(0,r.jsx)(s,{className:"w-4 h-4"}),(0,r.jsx)("span",{className:"font-medium",children:e.label}),(0,r.jsx)("span",{className:(0,g.cn)("px-1.5 py-0.5 text-xs rounded-full",o?"bg-white/20 text-white":"bg-primary/10 text-primary"),children:e.count})]}),o&&(0,r.jsx)(n.P.div,{className:"absolute bottom-0 left-0 right-0 h-0.5 bg-white",layoutId:"activeFilter",initial:!1,transition:{type:"spring",stiffness:500,damping:30}})]})},e.id)})})}function P(e){let{searchTerm:t,onSearchChange:a,placeholder:i="Search projects...",className:s}=e;return(0,r.jsxs)("div",{className:(0,g.cn)("relative max-w-md mx-auto",s),children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{type:"text",value:t,onChange:e=>a(e.target.value),placeholder:i,className:(0,g.cn)("w-full px-4 py-3 pl-12 rounded-lg border border-border/50 bg-background/50 backdrop-blur-sm","text-foreground placeholder:text-foreground/50","focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary/50","transition-all duration-300")}),(0,r.jsx)("div",{className:"absolute left-4 top-1/2 transform -translate-y-1/2",children:(0,r.jsx)("svg",{className:"w-5 h-5 text-foreground/50",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),t&&(0,r.jsx)("button",{onClick:()=>a(""),className:"absolute right-3 top-1/2 transform -translate-y-1/2 p-1 rounded-full hover:bg-foreground/10 transition-colors",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-foreground/50",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),t&&(0,r.jsxs)(n.P.div,{className:"absolute top-full mt-2 left-0 right-0 text-center text-sm text-foreground/60",initial:{opacity:0,y:-10},animate:{opacity:1,y:0},transition:{duration:.2},children:['Searching for "',t,'"']})]})}let A=[{value:"date",label:"Latest First"},{value:"name",label:"Name A-Z"},{value:"category",label:"Category"},{value:"status",label:"Status"}];function B(e){let{sortBy:t,onSortChange:a,className:i}=e;return(0,r.jsxs)("div",{className:(0,g.cn)("flex items-center space-x-2",i),children:[(0,r.jsx)("span",{className:"text-sm text-foreground/70",children:"Sort by:"}),(0,r.jsx)("select",{value:t,onChange:e=>a(e.target.value),className:(0,g.cn)("px-3 py-2 rounded-lg border border-border/50 bg-background/50 backdrop-blur-sm","text-foreground text-sm","focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary/50","transition-all duration-300"),children:A.map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))})]})}let J=[{id:1,title:"Healthcare Management System",description:"Full stack web application built with Java Spring Boot backend and Bootstrap frontend for managing patient records, appointments, and medical history.",longDescription:"A comprehensive healthcare management system featuring secure patient data management, appointment scheduling, medical history tracking, and role-based access control. Built using Java Spring Boot for robust backend services and Bootstrap for responsive frontend design.",image:"https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=800&h=600&fit=crop&crop=center",technologies:["Java","Spring Boot","HTML5","CSS3","JavaScript","Bootstrap","MySQL","REST APIs"],category:"web",githubUrl:"https://github.com",liveUrl:"#",demoUrl:"#",date:"2024",team:"College Project",status:"completed",featured:!0},{id:2,title:"E-Commerce Web Application",description:"Complete e-commerce platform built with Java Spring Boot and Bootstrap featuring product catalog, shopping cart, and payment integration.",longDescription:"A full-featured e-commerce web application with modern UI/UX design. Includes product management, shopping cart functionality, user authentication, order processing, and secure payment integration. Built using Java Spring Boot for backend services and Bootstrap for responsive frontend.",image:"https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=800&h=600&fit=crop&crop=center",technologies:["Java","Spring Boot","HTML5","CSS3","JavaScript","Bootstrap","MySQL","Payment Gateway"],category:"web",githubUrl:"https://github.com",liveUrl:"#",date:"2024",team:"College Project",status:"completed",featured:!1},{id:3,title:"Student Management System",description:"Comprehensive web application for managing student records, courses, grades, and attendance with role-based access control.",longDescription:"A complete student management system built with Java Spring Boot and Bootstrap. Features include student enrollment, course management, grade tracking, attendance monitoring, and report generation. Implements role-based access for students, teachers, and administrators with secure authentication.",image:"https://images.unsplash.com/photo-1523240795612-9a054b0db644?w=800&h=600&fit=crop&crop=center",technologies:["Java","Spring Boot","HTML5","CSS3","JavaScript","Bootstrap","MySQL","JPA"],category:"web",githubUrl:"https://github.com",liveUrl:"#",date:"2024",team:"College Project",status:"completed",featured:!1},{id:4,title:"Enterprise Web Applications",description:"Professional web applications built during internship using Java Spring Boot backend and Bootstrap frontend with enterprise-grade features.",longDescription:"Enterprise-level web applications developed during internship at CAUSEVE TECHNOLOGIES LLP. Features include secure authentication, role-based access control, real-time data processing, and responsive design using Java Spring Boot, HTML5, CSS3, JavaScript, and Bootstrap.",image:"https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=600&fit=crop&crop=center",technologies:["Java","Spring Boot","HTML5","CSS3","JavaScript","Bootstrap","MySQL","REST APIs"],category:"web",githubUrl:"https://github.com",liveUrl:"#",date:"2024",team:"Internship Project",status:"completed",featured:!1},{id:5,title:"Java Backend Automation System",description:"Enterprise automation system built with Java for monitoring, alerting, and task scheduling with email notifications and web dashboard.",longDescription:"Developed a comprehensive automation system using Java Spring Boot during internship at CYBERNAUT EDUTECH. Features include automated monitoring, email notification system using JavaMail API, task scheduling, and a web-based dashboard for system management and reporting.",image:"https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=600&fit=crop&crop=center",technologies:["Java","Spring Boot","JavaMail API","HTML5","CSS3","JavaScript","MySQL","Task Scheduling"],category:"system",githubUrl:"https://github.com",liveUrl:"https://cybernaut.co.in/",date:"2024",team:"Internship Project",status:"completed",featured:!1},{id:6,title:"Full Stack Portfolio Website",description:"Modern, responsive portfolio website showcasing Full Stack development skills with Java backend expertise and frontend technologies.",longDescription:"A professional portfolio website built to showcase Full Stack development expertise. Features responsive design using HTML5, CSS3, JavaScript, and Bootstrap, with backend capabilities demonstrated through Java Spring Boot projects. Includes interactive elements, smooth animations, and modern UI/UX design.",image:"https://images.unsplash.com/photo-1467232004584-a241de8bcf5d?w=800&h=600&fit=crop&crop=center",technologies:["HTML5","CSS3","JavaScript","Bootstrap","Java","Spring Boot"],category:"web",githubUrl:"https://github.com",liveUrl:"#",date:"2024",team:"Personal Project",status:"completed",featured:!1}],T=[{id:"all",label:"All Projects",icon:"Globe",count:J.length},{id:"web",label:"Full Stack",icon:"Code",count:J.filter(e=>"web"===e.category).length},{id:"system",label:"Backend",icon:"Cpu",count:J.filter(e=>"system"===e.category).length},{id:"frontend",label:"Frontend",icon:"Globe",count:J.filter(e=>"frontend"===e.category).length},{id:"enterprise",label:"Enterprise",icon:"Database",count:J.filter(e=>"enterprise"===e.category).length}];function L(){let[e,t]=(0,i.useState)("all"),[a,l]=(0,i.useState)(""),[c,d]=(0,i.useState)("date"),[m,p]=(0,i.useState)(null),u=(0,i.useRef)(null),h=(0,s.W)(u,{once:!0,margin:"-100px"}),g=(0,i.useMemo)(()=>{let t=J;return"all"!==e&&(t=t.filter(t=>t.category===e)),a&&(t=t.filter(e=>e.title.toLowerCase().includes(a.toLowerCase())||e.description.toLowerCase().includes(a.toLowerCase())||e.technologies.some(e=>e.toLowerCase().includes(a.toLowerCase())))),t.sort((e,t)=>{switch(c){case"name":return e.title.localeCompare(t.title);case"category":return e.category.localeCompare(t.category);case"status":return e.status.localeCompare(t.status);default:return new Date(t.date).getTime()-new Date(e.date).getTime()}}),t},[e,a,c]);return(0,r.jsxs)("section",{ref:u,className:"py-20 bg-muted/20 relative overflow-hidden",children:[(0,r.jsx)("div",{className:"absolute inset-0 opacity-5",children:(0,r.jsx)("div",{className:"absolute inset-0",style:{backgroundImage:"\n            radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),\n            radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),\n            radial-gradient(circle at 40% 40%, rgba(0, 255, 136, 0.05) 0%, transparent 50%)\n          "}})}),(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10",children:[(0,r.jsxs)(n.P.div,{className:"text-center mb-16",initial:{opacity:0,y:30},animate:h?{opacity:1,y:0}:{opacity:0,y:30},transition:{duration:.6},children:[(0,r.jsx)("h2",{className:"text-4xl md:text-5xl font-bold gradient-text mb-6",children:"Featured Projects"}),(0,r.jsx)("p",{className:"text-xl text-foreground/80 max-w-3xl mx-auto leading-relaxed",children:"A showcase of my Full Stack development expertise through real-world projects spanning Java backend development, frontend technologies, and enterprise web applications. Each project demonstrates problem-solving skills, clean code practices, and modern development methodologies."})]}),(0,r.jsxs)(n.P.div,{className:"mb-12 space-y-6",initial:{opacity:0,y:20},animate:h?{opacity:1,y:0}:{opacity:0,y:20},transition:{duration:.6,delay:.2},children:[(0,r.jsx)(P,{searchTerm:a,onSearchChange:l,placeholder:"Search projects by name, description, or technology..."}),(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6",children:[(0,r.jsx)(C,{categories:T,activeFilter:e,onFilterChange:t}),(0,r.jsx)(B,{sortBy:c,onSortChange:d})]})]}),(0,r.jsx)(n.P.div,{initial:{opacity:0,y:30},animate:h?{opacity:1,y:0}:{opacity:0,y:30},transition:{duration:.6,delay:.4},children:(0,r.jsx)(o.N,{mode:"wait",children:g.length>0?(0,r.jsx)(n.P.div,{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.3},children:g.map((e,t)=>(0,r.jsx)(b,{...e,delay:100*t,onViewDetails:()=>p(e)},e.id))},"".concat(e,"-").concat(a,"-").concat(c)):(0,r.jsxs)(n.P.div,{className:"text-center py-16",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},children:[(0,r.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDD0D"}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-foreground mb-2",children:"No projects found"}),(0,r.jsx)("p",{className:"text-foreground/70",children:"Try adjusting your search terms or filters to find what you're looking for."})]})})}),(0,r.jsx)(n.P.div,{className:"mt-16 grid grid-cols-2 md:grid-cols-4 gap-6",initial:{opacity:0,y:30},animate:h?{opacity:1,y:0}:{opacity:0,y:30},transition:{duration:.6,delay:.6},children:[{label:"Total Projects",value:J.length},{label:"Completed",value:J.filter(e=>"completed"===e.status).length},{label:"Technologies",value:[...new Set(J.flatMap(e=>e.technologies))].length},{label:"GitHub Stars",value:"150+"}].map((e,t)=>(0,r.jsx)(x.Zp,{variant:"glass",className:"text-center hover:glow-blue transition-all duration-300",children:(0,r.jsxs)(x.Wu,{className:"p-4",children:[(0,r.jsx)("div",{className:"text-2xl font-bold gradient-text mb-1",children:e.value}),(0,r.jsx)("div",{className:"text-sm text-foreground/70",children:e.label})]})},e.label))})]})]})}},3196:(e,t,a)=>{"use strict";a.d(t,{default:()=>w});var r=a(5155),i=a(2115),s=a(6604),n=a(4407),o=a(7576),l=a(9621),c=a(9037),d=a(7949),m=a(9074),p=a(4516),u=a(3052),x=a(3786),h=a(6695),g=a(285),f=a(9434);let b={internship:{color:"from-cyber-blue to-hologram-blue",icon:"\uD83D\uDCBC",label:"Internship"},project:{color:"from-electric-violet to-quantum-purple",icon:"\uD83D\uDE80",label:"Project"},education:{color:"from-neon-green to-matrix-green",icon:"\uD83C\uDF93",label:"Education"},certification:{color:"from-plasma-pink to-red-500",icon:"\uD83C\uDFC6",label:"Certification"}};function v(e){let{title:t,company:a,location:o,duration:l,type:c,description:d,achievements:v,technologies:y,link:j,isLeft:N=!1,delay:w=0,className:S}=e,k=(0,i.useRef)(null),C=(0,s.W)(k,{once:!0,margin:"-100px"}),P=b[c];return(0,r.jsxs)(n.P.div,{ref:k,className:(0,f.cn)("relative flex items-center",N?"md:flex-row-reverse":"md:flex-row",S),initial:{opacity:0,x:N?50:-50},animate:C?{opacity:1,x:0}:{opacity:0,x:N?50:-50},transition:{duration:.6,delay:w/1e3},children:[(0,r.jsx)("div",{className:"hidden md:flex flex-col items-center absolute left-1/2 transform -translate-x-1/2 z-10",children:(0,r.jsx)(n.P.div,{className:(0,f.cn)("w-4 h-4 rounded-full bg-gradient-to-r border-4 border-background shadow-lg",P.color),initial:{scale:0},animate:C?{scale:1}:{scale:0},transition:{duration:.3,delay:(w+200)/1e3}})}),(0,r.jsx)("div",{className:(0,f.cn)("w-full md:w-5/12",N?"md:pr-8":"md:pl-8"),children:(0,r.jsx)(h._e,{className:"group hover:scale-[1.02] transition-all duration-300",children:(0,r.jsxs)(h.Wu,{className:"p-6",children:[(0,r.jsx)("div",{className:"flex items-start justify-between mb-4",children:(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,r.jsx)("span",{className:"text-lg",children:P.icon}),(0,r.jsx)("span",{className:(0,f.cn)("px-2 py-1 text-xs font-medium rounded-full bg-gradient-to-r text-white",P.color),children:P.label})]}),(0,r.jsx)("h3",{className:"text-xl font-bold text-foreground group-hover:text-primary transition-colors",children:t}),(0,r.jsx)("p",{className:"text-lg font-medium text-primary mb-1",children:a}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:space-x-4 text-sm text-foreground/70",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(m.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:l})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(p.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:o})]})]})]})}),(0,r.jsx)("p",{className:"text-foreground/80 mb-4 leading-relaxed",children:d}),v.length>0&&(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("h4",{className:"text-sm font-semibold text-foreground mb-2",children:"Key Achievements:"}),(0,r.jsx)("ul",{className:"space-y-1",children:v.map((e,t)=>(0,r.jsxs)(n.P.li,{className:"flex items-start space-x-2 text-sm text-foreground/80",initial:{opacity:0,x:-10},animate:C?{opacity:1,x:0}:{opacity:0,x:-10},transition:{duration:.3,delay:(w+300+100*t)/1e3},children:[(0,r.jsx)(u.A,{className:"w-3 h-3 text-primary mt-0.5 flex-shrink-0"}),(0,r.jsx)("span",{children:e})]},t))})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("h4",{className:"text-sm font-semibold text-foreground mb-2",children:"Technologies Used:"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:y.map((e,t)=>(0,r.jsx)(n.P.span,{className:"px-2 py-1 text-xs bg-primary/10 text-primary rounded-md border border-primary/20",initial:{opacity:0,scale:.8},animate:C?{opacity:1,scale:1}:{opacity:0,scale:.8},transition:{duration:.3,delay:(w+400+50*t)/1e3},children:e},e))})]}),j&&(0,r.jsx)(g.$,{variant:"outline",size:"sm",className:"group/btn",asChild:!0,children:(0,r.jsxs)("a",{href:j,target:"_blank",rel:"noopener noreferrer",children:[(0,r.jsx)("span",{children:"Learn More"}),(0,r.jsx)(x.A,{className:"w-3 h-3 ml-2 group-hover/btn:translate-x-1 transition-transform"})]})})]})})}),(0,r.jsxs)("div",{className:"md:hidden absolute left-4 top-6",children:[(0,r.jsx)("div",{className:(0,f.cn)("w-3 h-3 rounded-full bg-gradient-to-r",P.color)}),(0,r.jsx)("div",{className:"w-0.5 bg-gradient-to-b from-primary/50 to-transparent h-full absolute left-1/2 transform -translate-x-1/2 top-3"})]})]})}function y(e){let{items:t,className:a}=e;return(0,r.jsxs)("div",{className:(0,f.cn)("relative",a),children:[(0,r.jsx)("div",{className:"hidden md:block absolute left-1/2 transform -translate-x-1/2 w-0.5 bg-gradient-to-b from-primary/50 via-primary/30 to-primary/50 h-full"}),(0,r.jsx)("div",{className:"md:hidden absolute left-4 w-0.5 bg-gradient-to-b from-primary/50 via-primary/30 to-primary/50 h-full"}),(0,r.jsx)("div",{className:"space-y-12 md:space-y-16",children:t.map((e,t)=>(0,r.jsx)(v,{...e,isLeft:t%2==1,delay:200*t,className:"md:pl-0 pl-12"},"".concat(e.title,"-").concat(e.company)))})]})}let j=[{title:"Full Stack Web Developer Intern",company:"CAUSEVE TECHNOLOGIES LLP",location:"On-site",duration:"2024",type:"internship",description:"Completed a comprehensive full stack web development internship specializing in Java backend and modern frontend technologies. Built enterprise-level web applications using HTML, CSS, JavaScript, Bootstrap for frontend and Java with MySQL for backend operations.",achievements:["Developed full stack web applications using Java backend and HTML/CSS/JavaScript frontend","Built responsive user interfaces using Bootstrap and modern CSS techniques","Implemented RESTful APIs and backend services using Java and Spring Boot","Designed and managed MySQL databases with optimized queries","Created CRUD operations and real-time web application features","Gained expertise in full stack development lifecycle"],technologies:["Java","Spring Boot","HTML5","CSS3","JavaScript","Bootstrap","MySQL","REST APIs"],link:"#"},{title:"Backend Java Developer Intern",company:"CYBERNAUT EDUTECH PVT LTD",location:"Remote",duration:"2024",type:"internship",description:"Specialized in Java backend development and automation systems. Developed enterprise-grade applications including an automated price monitoring system with email notifications, demonstrating expertise in Java programming and system integration.",achievements:["Built automated price monitoring system using Java and web scraping","Implemented email notification system using JavaMail API","Created scheduled tasks and background services using Java","Developed REST APIs for system integration","Optimized application performance and database queries","Delivered production-ready Java applications"],technologies:["Java","Spring Boot","JavaMail API","REST APIs","MySQL","Task Scheduling","Web Scraping"],link:"https://cybernaut.co.in/"},{title:"Healthcare Management System",company:"College Project",location:"Mahendra Engineering College",duration:"2024",type:"project",description:"Developed a comprehensive healthcare management web application using Java Spring Boot backend and Bootstrap frontend. The system manages patient records, appointments, and medical history with secure authentication and responsive design.",achievements:["Built full stack web application using Java Spring Boot and Bootstrap","Implemented secure user authentication and authorization","Created responsive frontend with HTML5, CSS3, and JavaScript","Designed MySQL database with optimized schema","Developed RESTful APIs for frontend-backend communication","Implemented CRUD operations for patient and appointment management"],technologies:["Java","Spring Boot","HTML5","CSS3","JavaScript","Bootstrap","MySQL","REST APIs"],link:"#"},{title:"E-Commerce Web Application",company:"College Project",location:"Mahendra Engineering College",duration:"2024",type:"project",description:"Built a complete e-commerce web application using Java Spring Boot backend and responsive Bootstrap frontend. Features include product catalog, shopping cart, user authentication, order management, and payment integration with modern UI/UX design.",achievements:["Developed full stack e-commerce platform using Java and Bootstrap","Implemented shopping cart and order management system","Created responsive product catalog with search and filtering","Built secure user authentication and session management","Integrated payment gateway and order tracking","Designed modern UI with Bootstrap and custom CSS"],technologies:["Java","Spring Boot","HTML5","CSS3","JavaScript","Bootstrap","MySQL","Payment Gateway"],link:"#"},{title:"Student Management System",company:"College Project",location:"Mahendra Engineering College",duration:"2024",type:"project",description:"Developed a comprehensive student management web application using Java Spring Boot and Bootstrap. The system manages student records, course enrollment, grades, attendance, and generates reports with role-based access control for students, teachers, and administrators.",achievements:["Built complete student management system using Java full stack","Implemented role-based access control (Student, Teacher, Admin)","Created responsive dashboard with Bootstrap and JavaScript","Developed grade management and attendance tracking","Built report generation system with PDF export","Implemented real-time notifications and messaging system"],technologies:["Java","Spring Boot","HTML5","CSS3","JavaScript","Bootstrap","MySQL","JPA"],link:"#"},{title:"B.Tech in Artificial Intelligence & Data Science",company:"Mahendra Engineering College",location:"Tamil Nadu, India",duration:"2021 - 2025",type:"education",description:"Comprehensive program with specialization in Full Stack Development using Java backend and modern frontend technologies. Strong focus on enterprise application development, web technologies, and software engineering principles.",achievements:["Specialized in Java Full Stack Development and web technologies","Completed multiple enterprise-level web application projects","Gained expertise in Java, Spring Boot, HTML, CSS, JavaScript, Bootstrap","Developed practical skills in database design and REST API development","Strong foundation in software engineering and system design"],technologies:["Java","Spring Boot","HTML5","CSS3","JavaScript","Bootstrap","MySQL","REST APIs"],link:"#"},{title:"Higher Secondary Education",company:"Sathya Saai Matric Higher Secondary School",location:"Tamil Nadu, India",duration:"2020 - 2021",type:"education",description:"Higher Secondary Certificate (HSC) with strong foundation in mathematics, science, and computer applications, preparing for engineering studies.",achievements:["Completed HSC with excellent academic performance","Strong foundation in mathematics and science","Developed interest in computer science and technology","Prepared for engineering entrance examinations"],technologies:["Mathematics","Physics","Chemistry","Computer Science"],link:"#"}],N=[{label:"Internships Completed",value:"2",icon:o.A},{label:"Projects Built",value:"15+",icon:l.A},{label:"Certifications",value:"6",icon:c.A},{label:"Years of Learning",value:"4",icon:d.A}];function w(){let e=(0,i.useRef)(null),t=(0,s.W)(e,{once:!0,margin:"-100px"});return(0,r.jsxs)("section",{ref:e,className:"py-20 bg-background relative overflow-hidden",children:[(0,r.jsx)("div",{className:"absolute inset-0 opacity-5",children:(0,r.jsx)("div",{className:"absolute inset-0",style:{backgroundImage:"\n            linear-gradient(45deg, rgba(0, 212, 255, 0.1) 0%, transparent 50%),\n            linear-gradient(-45deg, rgba(139, 92, 246, 0.1) 0%, transparent 50%)\n          "}})}),(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10",children:[(0,r.jsxs)(n.P.div,{className:"text-center mb-16",initial:{opacity:0,y:30},animate:t?{opacity:1,y:0}:{opacity:0,y:30},transition:{duration:.6},children:[(0,r.jsx)("h2",{className:"text-4xl md:text-5xl font-bold gradient-text mb-6",children:"Experience & Journey"}),(0,r.jsx)("p",{className:"text-xl text-foreground/80 max-w-3xl mx-auto leading-relaxed",children:"My professional journey through internships, projects, and continuous learning in Full Stack development. Each experience has shaped my expertise in Java backend development, frontend technologies, and enterprise web applications."})]}),(0,r.jsx)(n.P.div,{className:"grid grid-cols-2 md:grid-cols-4 gap-6 mb-16",initial:{opacity:0,y:30},animate:t?{opacity:1,y:0}:{opacity:0,y:30},transition:{duration:.6,delay:.2},children:N.map((e,a)=>{let{label:i,value:s,icon:o}=e;return(0,r.jsx)(n.P.div,{initial:{opacity:0,scale:.8},animate:t?{opacity:1,scale:1}:{opacity:0,scale:.8},transition:{duration:.4,delay:100*a/1e3},children:(0,r.jsx)(h.Zp,{variant:"glass",className:"text-center hover:glow-blue transition-all duration-300",children:(0,r.jsxs)(h.Wu,{className:"p-6",children:[(0,r.jsx)(o,{className:"w-8 h-8 text-primary mx-auto mb-3"}),(0,r.jsx)("div",{className:"text-3xl font-bold gradient-text mb-1",children:s}),(0,r.jsx)("div",{className:"text-sm text-foreground/70",children:i})]})})},i)})}),(0,r.jsx)(n.P.div,{initial:{opacity:0,y:30},animate:t?{opacity:1,y:0}:{opacity:0,y:30},transition:{duration:.6,delay:.4},children:(0,r.jsx)(y,{items:j})}),(0,r.jsx)(n.P.div,{className:"text-center mt-16",initial:{opacity:0,y:30},animate:t?{opacity:1,y:0}:{opacity:0,y:30},transition:{duration:.6,delay:.6},children:(0,r.jsx)(h.Zp,{variant:"gradient",className:"max-w-2xl mx-auto",children:(0,r.jsxs)(h.Wu,{className:"p-8 text-center",children:[(0,r.jsx)("h3",{className:"text-2xl font-bold text-foreground mb-4",children:"Ready for New Challenges"}),(0,r.jsx)("p",{className:"text-foreground/80 mb-6",children:"I'm actively seeking opportunities to apply my skills in Java backend development, frontend technologies, and full-stack web development to solve real-world problems and create innovative solutions."}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,r.jsx)(n.P.a,{href:"#contact",className:"px-6 py-3 bg-gradient-to-r from-cyber-blue to-hologram-blue text-white rounded-lg font-medium hover:shadow-lg transition-all duration-300",whileHover:{scale:1.05},whileTap:{scale:.95},children:"Get In Touch"}),(0,r.jsx)(n.P.a,{href:"#projects",className:"px-6 py-3 border border-primary/50 text-primary rounded-lg font-medium hover:bg-primary/10 transition-all duration-300",whileHover:{scale:1.05},whileTap:{scale:.95},children:"View Projects"})]})]})})})]})]})}},3828:(e,t,a)=>{"use strict";a.d(t,{default:()=>J});var r=a(5155),i=a(2115),s=a(6604),n=a(4407),o=a(9621),l=a(3314),c=a(4213),d=a(9376),m=a(6785),p=a(1539),u=a(6695),x=a(9434);let h={blue:{bg:"from-cyber-blue to-hologram-blue",glow:"shadow-[0_0_20px_rgba(0,212,255,0.3)]"},purple:{bg:"from-electric-violet to-quantum-purple",glow:"shadow-[0_0_20px_rgba(139,92,246,0.3)]"},green:{bg:"from-neon-green to-matrix-green",glow:"shadow-[0_0_20px_rgba(0,255,136,0.3)]"},pink:{bg:"from-plasma-pink to-red-500",glow:"shadow-[0_0_20px_rgba(255,0,110,0.3)]"},orange:{bg:"from-orange-500 to-yellow-500",glow:"shadow-[0_0_20px_rgba(249,115,22,0.3)]"}};function g(e){let{name:t,percentage:a,icon:o,color:l="blue",delay:c=0,className:d}=e,m=(0,i.useRef)(null),p=(0,s.W)(m,{once:!0,margin:"-100px"}),[u,g]=(0,i.useState)(0);(0,i.useEffect)(()=>{if(p){let e=setTimeout(()=>{g(a)},c);return()=>clearTimeout(e)}},[p,a,c]);let f=h[l];return(0,r.jsxs)(n.P.div,{ref:m,className:(0,x.cn)("space-y-2",d),initial:{opacity:0,y:20},animate:p?{opacity:1,y:0}:{opacity:0,y:20},transition:{duration:.6,delay:c/1e3},children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[o&&(0,r.jsx)("span",{className:"text-foreground/80",children:o}),(0,r.jsx)("span",{className:"text-sm font-medium text-foreground",children:t})]}),(0,r.jsxs)(n.P.span,{className:"text-sm font-bold text-primary",initial:{opacity:0},animate:p?{opacity:1}:{opacity:0},transition:{duration:.3,delay:(c+500)/1e3},children:[u,"%"]})]}),(0,r.jsxs)("div",{className:"relative h-2 bg-foreground/10 rounded-full overflow-hidden",children:[(0,r.jsxs)(n.P.div,{className:(0,x.cn)("h-full bg-gradient-to-r rounded-full relative",f.bg),initial:{width:0},animate:p?{width:"".concat(a,"%")}:{width:0},transition:{duration:1.5,delay:(c+200)/1e3,ease:"easeOut"},children:[(0,r.jsx)("div",{className:(0,x.cn)("absolute inset-0 rounded-full opacity-60",f.glow)}),(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -skew-x-12 animate-shimmer"})]}),(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300"})]})]})}function f(e){let{title:t,skills:a,className:o}=e,l=(0,i.useRef)(null),c=(0,s.W)(l,{once:!0,margin:"-50px"});return(0,r.jsxs)(n.P.div,{ref:l,className:(0,x.cn)("space-y-4",o),initial:{opacity:0,y:30},animate:c?{opacity:1,y:0}:{opacity:0,y:30},transition:{duration:.6},children:[(0,r.jsxs)("h3",{className:"text-xl font-semibold text-foreground mb-4 flex items-center",children:[(0,r.jsx)("span",{className:"w-2 h-2 bg-primary rounded-full mr-3"}),t]}),(0,r.jsx)("div",{className:"space-y-4",children:a.map((e,t)=>(0,r.jsx)(g,{name:e.name,percentage:e.percentage,icon:e.icon,color:e.color,delay:200*t},e.name))})]})}function b(e){let{name:t,percentage:a,size:o=120,strokeWidth:l=8,color:c="#00d4ff",delay:d=0}=e,m=(0,i.useRef)(null),p=(0,s.W)(m,{once:!0}),[u,x]=(0,i.useState)(0),h=(o-l)/2,g=2*h*Math.PI,f=g-u/100*g;return(0,i.useEffect)(()=>{if(p){let e=setTimeout(()=>{x(a)},d);return()=>clearTimeout(e)}},[p,a,d]),(0,r.jsxs)(n.P.div,{ref:m,className:"flex flex-col items-center space-y-2",initial:{opacity:0,scale:.8},animate:p?{opacity:1,scale:1}:{opacity:0,scale:.8},transition:{duration:.6,delay:d/1e3},children:[(0,r.jsxs)("div",{className:"relative",style:{width:o,height:o},children:[(0,r.jsxs)("svg",{className:"transform -rotate-90",width:o,height:o,children:[(0,r.jsx)("circle",{cx:o/2,cy:o/2,r:h,stroke:"currentColor",strokeWidth:l,fill:"none",className:"text-foreground/10"}),(0,r.jsx)(n.P.circle,{cx:o/2,cy:o/2,r:h,stroke:c,strokeWidth:l,fill:"none",strokeLinecap:"round",strokeDasharray:g,strokeDashoffset:f,initial:{strokeDashoffset:g},animate:p?{strokeDashoffset:f}:{strokeDashoffset:g},transition:{duration:1.5,delay:d/1e3,ease:"easeOut"},style:{filter:"drop-shadow(0 0 8px ".concat(c,"40)")}})]}),(0,r.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,r.jsxs)(n.P.span,{className:"text-lg font-bold text-foreground",initial:{opacity:0},animate:p?{opacity:1}:{opacity:0},transition:{duration:.3,delay:(d+500)/1e3},children:[u,"%"]})})]}),(0,r.jsx)("span",{className:"text-sm font-medium text-foreground/80 text-center",children:t})]})}var v=a(646),y=a(9074),j=a(9037),N=a(3786),w=a(285);let S={completed:{icon:v.A,color:"text-neon-green",bg:"bg-neon-green/10",border:"border-neon-green/30"},"in-progress":{icon:y.A,color:"text-cyber-blue",bg:"bg-cyber-blue/10",border:"border-cyber-blue/30"},planned:{icon:j.A,color:"text-electric-violet",bg:"bg-electric-violet/10",border:"border-electric-violet/30"}};function k(e){let{title:t,issuer:a,date:o,description:l,skills:c,credentialUrl:d,image:m,status:p,delay:h=0,className:g}=e,f=(0,i.useRef)(null),b=(0,s.W)(f,{once:!0,margin:"-100px"}),v=S[p],j=v.icon;return(0,r.jsx)(n.P.div,{ref:f,initial:{opacity:0,y:50},animate:b?{opacity:1,y:0}:{opacity:0,y:50},transition:{duration:.6,delay:h/1e3},className:g,children:(0,r.jsxs)(u.Zp,{variant:"neural",className:(0,x.cn)("group hover:scale-[1.02] transition-all duration-300 relative overflow-hidden",v.border),children:[(0,r.jsxs)("div",{className:(0,x.cn)("absolute top-4 right-4 px-2 py-1 rounded-full flex items-center space-x-1 text-xs font-medium",v.bg,v.color),children:[(0,r.jsx)(j,{className:"w-3 h-3"}),(0,r.jsx)("span",{className:"capitalize",children:p.replace("-"," ")})]}),(0,r.jsxs)(u.Wu,{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-start space-x-4 mb-4",children:[m&&(0,r.jsx)("div",{className:"flex-shrink-0 w-12 h-12 rounded-lg bg-gradient-to-br from-cyber-blue/20 to-electric-violet/20 flex items-center justify-center",children:(0,r.jsx)("img",{src:m,alt:a,className:"w-8 h-8 object-contain"})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-foreground group-hover:text-primary transition-colors",children:t}),(0,r.jsx)("p",{className:"text-sm text-foreground/70 mb-1",children:a}),(0,r.jsxs)("div",{className:"flex items-center text-xs text-foreground/60",children:[(0,r.jsx)(y.A,{className:"w-3 h-3 mr-1"}),o]})]})]}),l&&(0,r.jsx)("p",{className:"text-sm text-foreground/80 mb-4 leading-relaxed",children:l}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-foreground/90 mb-2",children:"Skills Covered:"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:c.map((e,t)=>(0,r.jsx)(n.P.span,{className:"px-2 py-1 text-xs bg-primary/10 text-primary rounded-md border border-primary/20",initial:{opacity:0,scale:.8},animate:b?{opacity:1,scale:1}:{opacity:0,scale:.8},transition:{duration:.3,delay:(h+100*t)/1e3},children:e},e))})]}),d&&"completed"===p&&(0,r.jsx)(w.$,{variant:"outline",size:"sm",className:"w-full group/btn",asChild:!0,children:(0,r.jsxs)("a",{href:d,target:"_blank",rel:"noopener noreferrer",children:[(0,r.jsx)("span",{children:"View Credential"}),(0,r.jsx)(N.A,{className:"w-3 h-3 ml-2 group-hover/btn:translate-x-1 transition-transform"})]})}),"in-progress"===p&&(0,r.jsxs)("div",{className:"mt-4",children:[(0,r.jsxs)("div",{className:"flex justify-between text-xs text-foreground/60 mb-1",children:[(0,r.jsx)("span",{children:"Progress"}),(0,r.jsx)("span",{children:"75%"})]}),(0,r.jsx)("div",{className:"w-full bg-foreground/10 rounded-full h-1.5",children:(0,r.jsx)(n.P.div,{className:"bg-gradient-to-r from-cyber-blue to-hologram-blue h-1.5 rounded-full",initial:{width:0},animate:b?{width:"75%"}:{width:0},transition:{duration:1,delay:(h+500)/1e3}})})]})]}),(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-primary/5 via-transparent to-secondary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"})]})})}function C(e){let{certifications:t,className:a}=e;return(0,r.jsx)("div",{className:(0,x.cn)("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",a),children:t.map((e,t)=>(0,r.jsx)(k,{...e,delay:200*t},"".concat(e.title,"-").concat(e.issuer)))})}let P={frontend:[{name:"HTML5",percentage:95,color:"orange",icon:(0,r.jsx)(o.A,{className:"w-4 h-4"})},{name:"CSS3",percentage:92,color:"blue",icon:(0,r.jsx)(o.A,{className:"w-4 h-4"})},{name:"JavaScript",percentage:90,color:"green",icon:(0,r.jsx)(o.A,{className:"w-4 h-4"})},{name:"Bootstrap",percentage:95,color:"purple",icon:(0,r.jsx)(o.A,{className:"w-4 h-4"})},{name:"Responsive Design",percentage:93,color:"pink",icon:(0,r.jsx)(o.A,{className:"w-4 h-4"})},{name:"jQuery",percentage:85,color:"blue",icon:(0,r.jsx)(o.A,{className:"w-4 h-4"})}],backend:[{name:"Java",percentage:95,color:"orange",icon:(0,r.jsx)(o.A,{className:"w-4 h-4"})},{name:"Spring Boot",percentage:88,color:"green",icon:(0,r.jsx)(l.A,{className:"w-4 h-4"})},{name:"REST APIs",percentage:90,color:"blue",icon:(0,r.jsx)(l.A,{className:"w-4 h-4"})},{name:"Microservices",percentage:82,color:"purple",icon:(0,r.jsx)(l.A,{className:"w-4 h-4"})},{name:"Maven",percentage:85,color:"purple",icon:(0,r.jsx)(o.A,{className:"w-4 h-4"})},{name:"JPA/Hibernate",percentage:80,color:"pink",icon:(0,r.jsx)(c.A,{className:"w-4 h-4"})}],database:[{name:"MySQL",percentage:90,color:"blue",icon:(0,r.jsx)(c.A,{className:"w-4 h-4"})},{name:"PostgreSQL",percentage:85,color:"green",icon:(0,r.jsx)(c.A,{className:"w-4 h-4"})},{name:"MongoDB",percentage:80,color:"purple",icon:(0,r.jsx)(c.A,{className:"w-4 h-4"})},{name:"SQL Server",percentage:82,color:"orange",icon:(0,r.jsx)(c.A,{className:"w-4 h-4"})},{name:"Database Design",percentage:88,color:"pink",icon:(0,r.jsx)(c.A,{className:"w-4 h-4"})},{name:"JDBC",percentage:85,color:"blue",icon:(0,r.jsx)(c.A,{className:"w-4 h-4"})}]},A=[{name:"Java Backend",percentage:95,color:"#00d4ff"},{name:"Frontend Dev",percentage:93,color:"#8b5cf6"},{name:"Full Stack",percentage:90,color:"#00ff88"},{name:"Database Design",percentage:88,color:"#ff006e"}],B=[{title:"Java Full Stack Development Certificate",issuer:"Programming Institute",date:"Completed 2024",description:"Comprehensive Java full stack development course covering Spring Boot, REST APIs, microservices, and enterprise application development.",skills:["Java","Spring Boot","REST APIs","Microservices","JPA"],status:"completed",credentialUrl:"#"},{title:"Frontend Web Development Certificate",issuer:"Web Development Institute",date:"Completed 2024",description:"Advanced frontend development course focusing on HTML5, CSS3, JavaScript, Bootstrap, and responsive web design principles.",skills:["HTML5","CSS3","JavaScript","Bootstrap","Responsive Design"],status:"completed",credentialUrl:"#"},{title:"Database Management Certificate",issuer:"Database Institute",date:"Completed 2024",description:"Comprehensive database management course covering SQL, database design, optimization, and integration with Java applications.",skills:["SQL","MySQL","Database Design","JDBC","Query Optimization"],status:"completed",credentialUrl:"#"},{title:"B.Tech AI & Data Science",issuer:"Mahendra Engineering College",date:"2021-2025",description:"Bachelor of Technology in Artificial Intelligence & Data Science with focus on machine learning, deep learning, and data analytics.",skills:["AI","Machine Learning","Data Science","Deep Learning"],status:"in-progress"},{title:"Higher Secondary Certificate (HSC)",issuer:"Sathya Saai Matric Higher Secondary School",date:"2020-2021",description:"Higher Secondary education with strong foundation in mathematics and science.",skills:["Mathematics","Physics","Chemistry","Computer Science"],status:"completed"},{title:"Secondary School Leaving Certificate (SSLC)",issuer:"Sathya Saai Matric Higher Secondary School",date:"2018-2019",description:"Secondary education with excellent academic performance and strong fundamentals.",skills:["Core Subjects","Mathematics","Science","English"],status:"completed"}];function J(){let e=(0,i.useRef)(null),t=(0,s.W)(e,{once:!0,margin:"-100px"});return(0,r.jsxs)("section",{ref:e,className:"py-20 bg-muted/20 relative overflow-hidden",children:[(0,r.jsx)("div",{className:"absolute inset-0 opacity-5",children:(0,r.jsx)("div",{className:"absolute inset-0",style:{backgroundImage:"\n            radial-gradient(circle at 25% 25%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),\n            radial-gradient(circle at 75% 75%, rgba(139, 92, 246, 0.1) 0%, transparent 50%)\n          "}})}),(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10",children:[(0,r.jsxs)(n.P.div,{className:"text-center mb-16",initial:{opacity:0,y:30},animate:t?{opacity:1,y:0}:{opacity:0,y:30},transition:{duration:.6},children:[(0,r.jsx)("h2",{className:"text-4xl md:text-5xl font-bold gradient-text mb-6",children:"About Me"}),(0,r.jsx)("p",{className:"text-xl text-foreground/80 max-w-3xl mx-auto leading-relaxed",children:"I am SANJAI S, a passionate Full Stack Developer specializing in Java backend development and modern frontend technologies. Expert in building scalable web applications using HTML, CSS, JavaScript, Bootstrap, and Java. I consider myself responsible and detail-oriented, ready to contribute to innovative projects."})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16",children:[(0,r.jsx)(n.P.div,{className:"lg:col-span-1",initial:{opacity:0,x:-50},animate:t?{opacity:1,x:0}:{opacity:0,x:-50},transition:{duration:.6,delay:.2},children:(0,r.jsx)(u._e,{className:"h-full",children:(0,r.jsxs)(u.Wu,{className:"p-6",children:[(0,r.jsxs)("div",{className:"text-center mb-6",children:[(0,r.jsx)("div",{className:"w-32 h-32 mx-auto mb-4 rounded-full bg-gradient-to-br from-cyber-blue/20 to-electric-violet/20 flex items-center justify-center",children:(0,r.jsx)(d.A,{className:"w-16 h-16 text-primary"})}),(0,r.jsx)("h3",{className:"text-2xl font-bold text-foreground mb-2",children:"SANJAI S"}),(0,r.jsx)("p",{className:"text-primary font-medium",children:"Full Stack Developer"}),(0,r.jsx)("p",{className:"text-foreground/70 text-sm",children:"Java Backend & Frontend Specialist"})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(m.A,{className:"w-5 h-5 text-primary"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-foreground",children:"Specialization"}),(0,r.jsx)("p",{className:"text-sm text-foreground/70",children:"Java Backend, Frontend Development, Full Stack"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(p.A,{className:"w-5 h-5 text-primary"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-foreground",children:"Passion"}),(0,r.jsx)("p",{className:"text-sm text-foreground/70",children:"Innovation & Problem Solving"})]})]})]})]})})}),(0,r.jsx)(n.P.div,{className:"lg:col-span-2",initial:{opacity:0,x:50},animate:t?{opacity:1,x:0}:{opacity:0,x:-50},transition:{duration:.6,delay:.4},children:(0,r.jsx)(u.Zp,{variant:"glow",className:"h-full",children:(0,r.jsxs)(u.Wu,{className:"p-6",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold text-foreground mb-6 text-center",children:"Core Competencies"}),(0,r.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-6",children:A.map((e,t)=>(0,r.jsx)(b,{name:e.name,percentage:e.percentage,color:e.color,delay:200*t},e.name))})]})})})]}),(0,r.jsxs)(n.P.div,{className:"mb-16",initial:{opacity:0,y:30},animate:t?{opacity:1,y:0}:{opacity:0,y:30},transition:{duration:.6,delay:.6},children:[(0,r.jsx)("h3",{className:"text-3xl font-bold text-center gradient-text mb-12",children:"Technical Skills"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[(0,r.jsx)(f,{title:"Frontend Technologies",skills:P.frontend}),(0,r.jsx)(f,{title:"Backend & Java",skills:P.backend}),(0,r.jsx)(f,{title:"Database & Tools",skills:P.database})]})]}),(0,r.jsxs)(n.P.div,{initial:{opacity:0,y:30},animate:t?{opacity:1,y:0}:{opacity:0,y:30},transition:{duration:.6,delay:.8},children:[(0,r.jsx)("h3",{className:"text-3xl font-bold text-center gradient-text mb-12",children:"Certifications & Courses"}),(0,r.jsx)(C,{certifications:B})]})]})]})}},4226:(e,t,a)=>{Promise.resolve().then(a.bind(a,9800)),Promise.resolve().then(a.bind(a,3828)),Promise.resolve().then(a.bind(a,4559)),Promise.resolve().then(a.bind(a,3196)),Promise.resolve().then(a.bind(a,848)),Promise.resolve().then(a.bind(a,1762))},4559:(e,t,a)=>{"use strict";a.d(t,{default:()=>J});var r=a(5155),i=a(2115),s=a(6604),n=a(4407),o=a(8883),l=a(9420),c=a(4516),d=a(9074),m=a(9099),p=a(2894),u=a(2177),x=a(8778),h=a(1153),g=a(1497),f=a(646),b=a(5339),v=a(1007),y=a(1154),j=a(2486),N=a(285),w=a(6695),S=a(9434);let k=h.z.object({name:h.z.string().min(2,"Name must be at least 2 characters"),email:h.z.string().email("Please enter a valid email address"),subject:h.z.string().min(5,"Subject must be at least 5 characters"),message:h.z.string().min(10,"Message must be at least 10 characters")});function C(e){let{className:t}=e,[a,l]=(0,i.useState)(!1),[c,d]=(0,i.useState)("idle"),m=(0,i.useRef)(null),p=(0,s.W)(m,{once:!0,margin:"-100px"}),{register:h,handleSubmit:C,formState:{errors:P},reset:A,watch:B}=(0,u.mN)({resolver:(0,x.u)(k)}),J=B(),T=async e=>{l(!0),d("idle");try{await new Promise(e=>setTimeout(e,2e3)),console.log("Form submitted:",e),d("success"),A()}catch(e){d("error")}finally{l(!1)}},L={focus:{scale:1.02,transition:{duration:.2}},blur:{scale:1,transition:{duration:.2}}};return(0,r.jsx)(n.P.div,{ref:m,className:(0,S.cn)("max-w-2xl mx-auto",t),initial:{opacity:0,y:30},animate:p?{opacity:1,y:0}:{opacity:0,y:30},transition:{duration:.6},children:(0,r.jsx)(w.Zp,{variant:"neural",className:"overflow-hidden",children:(0,r.jsxs)(w.Wu,{className:"p-8",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)(n.P.div,{className:"w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-cyber-blue/20 to-electric-violet/20 flex items-center justify-center",animate:{rotate:360},transition:{duration:20,repeat:1/0,ease:"linear"},children:(0,r.jsx)(g.A,{className:"w-8 h-8 text-primary"})}),(0,r.jsx)("h3",{className:"text-2xl font-bold text-foreground mb-2",children:"Let's Connect"}),(0,r.jsx)("p",{className:"text-foreground/70",children:"Ready to discuss your next project or just want to say hello? I'd love to hear from you!"})]}),"success"===c&&(0,r.jsxs)(n.P.div,{className:"mb-6 p-4 rounded-lg bg-neon-green/10 border border-neon-green/30 flex items-center space-x-3",initial:{opacity:0,y:-10},animate:{opacity:1,y:0},transition:{duration:.3},children:[(0,r.jsx)(f.A,{className:"w-5 h-5 text-neon-green"}),(0,r.jsx)("span",{className:"text-neon-green font-medium",children:"Message sent successfully! I'll get back to you soon."})]}),"error"===c&&(0,r.jsxs)(n.P.div,{className:"mb-6 p-4 rounded-lg bg-red-500/10 border border-red-500/30 flex items-center space-x-3",initial:{opacity:0,y:-10},animate:{opacity:1,y:0},transition:{duration:.3},children:[(0,r.jsx)(b.A,{className:"w-5 h-5 text-red-500"}),(0,r.jsx)("span",{className:"text-red-500 font-medium",children:"Something went wrong. Please try again later."})]}),(0,r.jsxs)("form",{onSubmit:C(T),className:"space-y-6",children:[(0,r.jsxs)(n.P.div,{className:"space-y-2",variants:L,whileFocus:"focus",initial:"blur",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-foreground",children:"Name *"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(v.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-foreground/50"}),(0,r.jsx)("input",{...h("name"),type:"text",placeholder:"Your full name",className:(0,S.cn)("w-full pl-12 pr-4 py-3 rounded-lg border bg-background/50 backdrop-blur-sm","text-foreground placeholder:text-foreground/50","focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary/50","transition-all duration-300",P.name?"border-red-500/50":"border-border/50")}),J.name&&!P.name&&(0,r.jsx)(f.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-neon-green"})]}),P.name&&(0,r.jsx)(n.P.p,{className:"text-sm text-red-500",initial:{opacity:0,y:-5},animate:{opacity:1,y:0},transition:{duration:.2},children:P.name.message})]}),(0,r.jsxs)(n.P.div,{className:"space-y-2",variants:L,whileFocus:"focus",initial:"blur",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-foreground",children:"Email *"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(o.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-foreground/50"}),(0,r.jsx)("input",{...h("email"),type:"email",placeholder:"<EMAIL>",className:(0,S.cn)("w-full pl-12 pr-4 py-3 rounded-lg border bg-background/50 backdrop-blur-sm","text-foreground placeholder:text-foreground/50","focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary/50","transition-all duration-300",P.email?"border-red-500/50":"border-border/50")}),J.email&&!P.email&&(0,r.jsx)(f.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-neon-green"})]}),P.email&&(0,r.jsx)(n.P.p,{className:"text-sm text-red-500",initial:{opacity:0,y:-5},animate:{opacity:1,y:0},transition:{duration:.2},children:P.email.message})]}),(0,r.jsxs)(n.P.div,{className:"space-y-2",variants:L,whileFocus:"focus",initial:"blur",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-foreground",children:"Subject *"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{...h("subject"),type:"text",placeholder:"What's this about?",className:(0,S.cn)("w-full px-4 py-3 rounded-lg border bg-background/50 backdrop-blur-sm","text-foreground placeholder:text-foreground/50","focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary/50","transition-all duration-300",P.subject?"border-red-500/50":"border-border/50")}),J.subject&&!P.subject&&(0,r.jsx)(f.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-neon-green"})]}),P.subject&&(0,r.jsx)(n.P.p,{className:"text-sm text-red-500",initial:{opacity:0,y:-5},animate:{opacity:1,y:0},transition:{duration:.2},children:P.subject.message})]}),(0,r.jsxs)(n.P.div,{className:"space-y-2",variants:L,whileFocus:"focus",initial:"blur",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-foreground",children:"Message *"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("textarea",{...h("message"),rows:5,placeholder:"Tell me about your project, ideas, or just say hello...",className:(0,S.cn)("w-full px-4 py-3 rounded-lg border bg-background/50 backdrop-blur-sm resize-none","text-foreground placeholder:text-foreground/50","focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary/50","transition-all duration-300",P.message?"border-red-500/50":"border-border/50")}),J.message&&!P.message&&(0,r.jsx)(f.A,{className:"absolute right-3 top-3 w-5 h-5 text-neon-green"})]}),P.message&&(0,r.jsx)(n.P.p,{className:"text-sm text-red-500",initial:{opacity:0,y:-5},animate:{opacity:1,y:0},transition:{duration:.2},children:P.message.message})]}),(0,r.jsx)(n.P.div,{whileHover:{scale:1.02},whileTap:{scale:.98},children:(0,r.jsxs)(N.$,{type:"submit",size:"lg",variant:"primary",disabled:a,className:"w-full group relative overflow-hidden",children:[a?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(y.A,{className:"w-5 h-5 mr-2 animate-spin"}),"Sending Message..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(j.A,{className:"w-5 h-5 mr-2 group-hover:translate-x-1 transition-transform"}),"Send Message"]}),(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700"})]})})]}),(0,r.jsx)("div",{className:"mt-8 text-center text-sm text-foreground/60",children:(0,r.jsx)("p",{children:"I typically respond within 24 hours. Looking forward to connecting with you!"})})]})})})}let P=[{icon:o.A,label:"Email",value:"<EMAIL>",href:"mailto:<EMAIL>",description:"Send me an email anytime"},{icon:l.A,label:"Phone",value:"+91 8072686247",href:"tel:+************",description:"Call me for urgent matters"},{icon:c.A,label:"Location",value:"Tamil Nadu, India",href:"#",description:"Available for remote work"},{icon:d.A,label:"Schedule",value:"Book a Meeting",href:"mailto:<EMAIL>",description:"Let's schedule a call"}],A=[{icon:m.A,label:"GitHub",href:"https://github.com/sanjai827054",color:"hover:text-foreground"},{icon:p.A,label:"LinkedIn",href:"https://linkedin.com/in/sanjai-s-ai",color:"hover:text-blue-500"},{icon:o.A,label:"Email",href:"mailto:<EMAIL>",color:"hover:text-green-400"},{icon:l.A,label:"Phone",href:"tel:+************",color:"hover:text-indigo-500"}],B=[{title:"Hire Me",description:"Looking for a Full Stack Java Developer?",action:"Get In Touch",color:"from-cyber-blue to-hologram-blue"},{title:"Collaborate",description:"Need help with web development projects?",action:"Let's Discuss",color:"from-electric-violet to-quantum-purple"},{title:"Learn Together",description:"Want to discuss Java and web technologies?",action:"Connect Now",color:"from-neon-green to-matrix-green"}];function J(){let e=(0,i.useRef)(null),t=(0,s.W)(e,{once:!0,margin:"-100px"});return(0,r.jsxs)("section",{ref:e,className:"py-20 bg-background relative overflow-hidden",children:[(0,r.jsx)("div",{className:"absolute inset-0 opacity-5",children:(0,r.jsx)("div",{className:"absolute inset-0",style:{backgroundImage:"\n            radial-gradient(circle at 25% 25%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),\n            radial-gradient(circle at 75% 75%, rgba(139, 92, 246, 0.1) 0%, transparent 50%)\n          "}})}),(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10",children:[(0,r.jsxs)(n.P.div,{className:"text-center mb-16",initial:{opacity:0,y:30},animate:t?{opacity:1,y:0}:{opacity:0,y:30},transition:{duration:.6},children:[(0,r.jsx)("h2",{className:"text-4xl md:text-5xl font-bold gradient-text mb-6",children:"Let's Work Together"}),(0,r.jsx)("p",{className:"text-xl text-foreground/80 max-w-3xl mx-auto leading-relaxed",children:"Ready to bring your ideas to life? Whether you're looking for a Full Stack Java Developer, frontend specialist, or just want to chat about web development and technology, I'm here to help."})]}),(0,r.jsx)(n.P.div,{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-16",initial:{opacity:0,y:30},animate:t?{opacity:1,y:0}:{opacity:0,y:30},transition:{duration:.6,delay:.2},children:B.map((e,a)=>(0,r.jsx)(n.P.div,{initial:{opacity:0,scale:.9},animate:t?{opacity:1,scale:1}:{opacity:0,scale:.9},transition:{duration:.4,delay:.1*a},children:(0,r.jsx)(w.Zp,{variant:"glass",className:"text-center hover:scale-105 transition-all duration-300 group cursor-pointer",children:(0,r.jsxs)(w.Wu,{className:"p-6",children:[(0,r.jsx)("div",{className:"w-12 h-12 mx-auto mb-4 rounded-full bg-gradient-to-r ".concat(e.color," flex items-center justify-center"),children:(0,r.jsx)("span",{className:"text-white text-xl",children:"\uD83D\uDCBC"})}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-foreground mb-2",children:e.title}),(0,r.jsx)("p",{className:"text-sm text-foreground/70 mb-4",children:e.description}),(0,r.jsx)(N.$,{variant:"outline",size:"sm",className:"group-hover:bg-primary/10",children:e.action})]})})},e.title))}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[(0,r.jsx)(n.P.div,{initial:{opacity:0,x:-50},animate:t?{opacity:1,x:0}:{opacity:0,x:-50},transition:{duration:.6,delay:.4},children:(0,r.jsx)(C,{})}),(0,r.jsxs)(n.P.div,{className:"space-y-8",initial:{opacity:0,x:50},animate:t?{opacity:1,x:0}:{opacity:0,x:50},transition:{duration:.6,delay:.6},children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("h3",{className:"text-2xl font-bold text-foreground mb-6",children:"Get In Touch"}),P.map((e,a)=>{let i=e.icon;return(0,r.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:t?{opacity:1,y:0}:{opacity:0,y:20},transition:{duration:.4,delay:.1*a},children:(0,r.jsx)(w._e,{className:"hover:scale-[1.02] transition-all duration-300 group",children:(0,r.jsx)(w.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center group-hover:bg-primary/20 transition-colors",children:(0,r.jsx)(i,{className:"w-6 h-6 text-primary"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h4",{className:"font-semibold text-foreground",children:e.label}),(0,r.jsx)("p",{className:"text-sm text-foreground/70 mb-1",children:e.description}),(0,r.jsx)("a",{href:e.href,className:"text-primary hover:text-primary/80 transition-colors font-medium",children:e.value})]})]})})})},e.label)})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-xl font-bold text-foreground mb-4",children:"Connect With Me"}),(0,r.jsx)("div",{className:"flex space-x-4",children:A.map((e,a)=>{let i=e.icon;return(0,r.jsx)(n.P.a,{href:e.href,target:"_blank",rel:"noopener noreferrer",className:"p-3 glass rounded-full hover:glow-blue transition-all duration-300 group ".concat(e.color),whileHover:{scale:1.1},whileTap:{scale:.95},initial:{opacity:0,scale:.8},animate:t?{opacity:1,scale:1}:{opacity:0,scale:.8},transition:{duration:.3,delay:.1*a},children:(0,r.jsx)(i,{className:"w-6 h-6 text-foreground group-hover:text-primary transition-colors"})},e.label)})})]}),(0,r.jsx)(n.P.div,{initial:{opacity:0,y:20},animate:t?{opacity:1,y:0}:{opacity:0,y:20},transition:{duration:.6,delay:.8},children:(0,r.jsx)(w.Zp,{variant:"gradient",className:"border border-neon-green/30",children:(0,r.jsxs)(w.Wu,{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 mb-3",children:[(0,r.jsx)("div",{className:"w-3 h-3 bg-neon-green rounded-full animate-pulse"}),(0,r.jsx)("span",{className:"font-semibold text-foreground",children:"Available for Work"})]}),(0,r.jsx)("p",{className:"text-sm text-foreground/80 leading-relaxed",children:"I'm currently seeking opportunities for my first work experience in Full Stack development. Whether it's internships, entry-level Java developer positions, or collaborative web development projects, I'd love to discuss how I can contribute to innovative software solutions."})]})})}),(0,r.jsxs)(n.P.div,{className:"text-center p-4 glass rounded-lg",initial:{opacity:0,y:20},animate:t?{opacity:1,y:0}:{opacity:0,y:20},transition:{duration:.6,delay:1},children:[(0,r.jsx)("div",{className:"text-2xl mb-2",children:"⚡"}),(0,r.jsxs)("p",{className:"text-sm text-foreground/70",children:[(0,r.jsx)("span",{className:"font-semibold text-primary",children:"Quick Response:"})," I typically reply within 24 hours"]})]})]})]})]})]})}},6695:(e,t,a)=>{"use strict";a.d(t,{Wu:()=>c,Zp:()=>l,_e:()=>d});var r=a(5155),i=a(2115),s=a(2085),n=a(9434);let o=(0,s.F)("rounded-xl border transition-all duration-300",{variants:{variant:{default:"glass border-border hover:glow-blue",solid:"bg-card border-border shadow-lg",gradient:"bg-gradient-to-br from-card via-card/80 to-card/60 border-border/50 hover:glow-purple",neural:"glass border-cyber-blue/30 hover:border-cyber-blue/60 hover:glow-blue neural-bg",glow:"glass border-electric-violet/30 hover:border-electric-violet/60 hover:glow-purple",minimal:"bg-transparent border-border/30 hover:border-border/60"},size:{sm:"p-4",default:"p-6",lg:"p-8",xl:"p-10"}},defaultVariants:{variant:"default",size:"default"}}),l=i.forwardRef((e,t)=>{let{className:a,variant:i,size:s,hover:l=!0,children:c,...d}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)(o({variant:i,size:s}),l&&"hover:scale-[1.02] hover:-translate-y-1",a),...d,children:c})});l.displayName="Card",i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 pb-4",a),...i})}).displayName="CardHeader",i.forwardRef((e,t)=>{let{className:a,children:i,...s}=e;return(0,r.jsx)("h3",{ref:t,className:(0,n.cn)("text-xl font-semibold leading-none tracking-tight text-foreground",a),...s,children:i})}).displayName="CardTitle",i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,r.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-foreground/70",a),...i})}).displayName="CardDescription";let c=i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("pt-0",a),...i})});c.displayName="CardContent",i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center pt-4",a),...i})}).displayName="CardFooter";let d=i.forwardRef((e,t)=>{let{className:a,children:i,...s}=e;return(0,r.jsxs)(l,{ref:t,variant:"neural",className:(0,n.cn)("relative overflow-hidden",a),...s,children:[(0,r.jsx)("div",{className:"absolute inset-0 opacity-10",children:(0,r.jsxs)("svg",{className:"w-full h-full",viewBox:"0 0 100 100",children:[(0,r.jsx)("defs",{children:(0,r.jsxs)("pattern",{id:"neural-pattern",x:"0",y:"0",width:"20",height:"20",patternUnits:"userSpaceOnUse",children:[(0,r.jsx)("circle",{cx:"10",cy:"10",r:"1",fill:"currentColor",opacity:"0.3"}),(0,r.jsx)("line",{x1:"10",y1:"10",x2:"30",y2:"10",stroke:"currentColor",strokeWidth:"0.5",opacity:"0.2"}),(0,r.jsx)("line",{x1:"10",y1:"10",x2:"10",y2:"30",stroke:"currentColor",strokeWidth:"0.5",opacity:"0.2"})]})}),(0,r.jsx)("rect",{width:"100%",height:"100%",fill:"url(#neural-pattern)"})]})}),(0,r.jsx)("div",{className:"relative z-10",children:i})]})});d.displayName="NeuralCard",i.forwardRef((e,t)=>{let{className:a,children:i,...s}=e;return(0,r.jsxs)(l,{ref:t,variant:"glow",className:(0,n.cn)("relative",a),...s,children:[(0,r.jsx)("div",{className:"absolute -inset-0.5 bg-gradient-to-r from-cyber-blue via-electric-violet to-neon-green rounded-xl opacity-30 group-hover:opacity-60 transition-opacity duration-300 animate-pulse"}),(0,r.jsx)("div",{className:"relative",children:i})]})}).displayName="GlowCard"},9434:(e,t,a)=>{"use strict";a.d(t,{cn:()=>s});var r=a(2596),i=a(9688);function s(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,i.QP)((0,r.$)(t))}},9800:(e,t,a)=>{"use strict";a.d(t,{default:()=>B});var r=a(5155),i=a(2115),s=a(4407),n=a(760),o=a(7340),l=a(1007),c=a(7576),d=a(9621),m=a(8883),p=a(4416),u=a(4783),x=a(285),h=a(1483),g=a(9434),f=a(5548);let b=[{name:"Home",href:"#home",icon:o.A},{name:"About",href:"#about",icon:l.A},{name:"Experience",href:"#experience",icon:c.A},{name:"Projects",href:"#projects",icon:d.A},{name:"Contact",href:"#contact",icon:m.A}];function v(){let[e,t]=(0,i.useState)(!1),[a,o]=(0,i.useState)("home"),[l,c]=(0,i.useState)(!1),d=(0,i.useRef)(null),m=(0,i.useRef)(null),v=(0,i.useRef)(null);(0,i.useEffect)(()=>{let e=()=>{c(window.scrollY>50);let e=b.map(e=>e.href.slice(1)).find(e=>{let t=document.getElementById(e);if(t){let e=t.getBoundingClientRect();return e.top<=100&&e.bottom>=100}return!1});e&&o(e)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]),(0,i.useEffect)(()=>{let e=f.os.context(()=>{f.os.fromTo(d.current,{y:-100,opacity:0},{y:0,opacity:1,duration:1,ease:"power3.out",delay:.5})},d);return()=>e.revert()},[]);let y=e=>{let a=document.getElementById(e.slice(1));a&&a.scrollIntoView({behavior:"smooth"}),t(!1)};return(0,r.jsxs)(s.P.nav,{ref:d,className:(0,g.cn)("fixed top-0 left-0 right-0 z-50 transition-all duration-300",l?"glass backdrop-blur-md border-b border-border/50":"bg-transparent"),initial:{y:-100,opacity:0},animate:{y:0,opacity:1},transition:{duration:1,delay:.5},children:[(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,r.jsx)(s.P.div,{ref:m,className:"flex-shrink-0",whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,r.jsx)("a",{href:"#home",onClick:e=>{e.preventDefault(),y("#home")},className:"text-2xl font-bold gradient-text",children:"AI.Portfolio"})}),(0,r.jsx)("div",{className:"hidden md:block",children:(0,r.jsx)("div",{className:"ml-10 flex items-baseline space-x-4",children:b.map(e=>{let{name:t,href:i,icon:n}=e;return(0,r.jsxs)(s.P.a,{href:i,onClick:e=>{e.preventDefault(),y(i)},className:(0,g.cn)("px-3 py-2 rounded-md text-sm font-medium transition-all duration-300 relative group",a===i.slice(1)?"text-primary":"text-foreground/70 hover:text-primary"),whileHover:{scale:1.05},whileTap:{scale:.95},children:[(0,r.jsxs)("span",{className:"flex items-center space-x-2",children:[(0,r.jsx)(n,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:t})]}),a===i.slice(1)&&(0,r.jsx)(s.P.div,{className:"absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-cyber-blue to-electric-violet",layoutId:"activeTab",initial:!1,transition:{type:"spring",stiffness:500,damping:30}}),(0,r.jsx)("div",{className:"absolute inset-0 rounded-md bg-primary/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"})]},t)})})}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(h.U,{}),(0,r.jsx)("div",{className:"md:hidden",children:(0,r.jsx)(x.$,{variant:"ghost",size:"icon",onClick:()=>t(!e),className:"relative",children:(0,r.jsx)(n.N,{mode:"wait",children:e?(0,r.jsx)(s.P.div,{initial:{rotate:-90,opacity:0},animate:{rotate:0,opacity:1},exit:{rotate:90,opacity:0},transition:{duration:.2},children:(0,r.jsx)(p.A,{className:"w-6 h-6"})},"close"):(0,r.jsx)(s.P.div,{initial:{rotate:90,opacity:0},animate:{rotate:0,opacity:1},exit:{rotate:-90,opacity:0},transition:{duration:.2},children:(0,r.jsx)(u.A,{className:"w-6 h-6"})},"menu")})})})]})]})}),(0,r.jsx)(n.N,{children:e&&(0,r.jsx)(s.P.div,{ref:v,className:"md:hidden glass backdrop-blur-md border-t border-border/50",initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3},children:(0,r.jsx)("div",{className:"px-2 pt-2 pb-3 space-y-1",children:b.map((e,t)=>{let{name:i,href:n,icon:o}=e;return(0,r.jsxs)(s.P.a,{href:n,onClick:e=>{e.preventDefault(),y(n)},className:(0,g.cn)("flex items-center space-x-3 px-3 py-2 rounded-md text-base font-medium transition-all duration-300",a===n.slice(1)?"text-primary bg-primary/10":"text-foreground/70 hover:text-primary hover:bg-primary/5"),initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.1*t},whileTap:{scale:.95},children:[(0,r.jsx)(o,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:i})]},i)})})})})]})}var y=a(2720),j=a(8417);function N(){let{scrollYProgress:e}=(0,y.L)(),t=(0,j.z)(e,{stiffness:100,damping:30,restDelta:.001});return(0,r.jsx)(s.P.div,{className:"fixed top-0 left-0 right-0 h-1 bg-gradient-to-r from-cyber-blue via-electric-violet to-neon-green z-50 origin-left",style:{scaleX:t}})}function w(){let[e,t]=(0,i.useState)(0);return(0,i.useEffect)(()=>{let e=()=>{let e=document.documentElement.scrollHeight-window.innerHeight;t(window.scrollY/e*100)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]),(0,r.jsx)("div",{className:"fixed bottom-8 right-8 z-50",children:(0,r.jsxs)("div",{className:"relative w-16 h-16",children:[(0,r.jsxs)("svg",{className:"w-16 h-16 transform -rotate-90",viewBox:"0 0 64 64",children:[(0,r.jsx)("circle",{cx:"32",cy:"32",r:"28",stroke:"currentColor",strokeWidth:"4",fill:"none",className:"text-foreground/20"}),(0,r.jsx)("circle",{cx:"32",cy:"32",r:"28",stroke:"url(#gradient)",strokeWidth:"4",fill:"none",strokeLinecap:"round",strokeDasharray:"".concat(2*Math.PI*28),strokeDashoffset:"".concat(2*Math.PI*28*(1-e/100)),className:"transition-all duration-300 ease-out"}),(0,r.jsx)("defs",{children:(0,r.jsxs)("linearGradient",{id:"gradient",x1:"0%",y1:"0%",x2:"100%",y2:"100%",children:[(0,r.jsx)("stop",{offset:"0%",stopColor:"#00d4ff"}),(0,r.jsx)("stop",{offset:"50%",stopColor:"#8b5cf6"}),(0,r.jsx)("stop",{offset:"100%",stopColor:"#00ff88"})]})})]}),(0,r.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,r.jsxs)("span",{className:"text-xs font-medium text-foreground",children:[Math.round(e),"%"]})})]})})}function S(){let[e,t]=(0,i.useState)(!1);return(0,i.useEffect)(()=>{let e=()=>{t(window.scrollY>300)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]),(0,r.jsx)(s.P.button,{className:"fixed bottom-8 left-8 p-3 glass rounded-full hover:glow-blue transition-all duration-300 z-50",onClick:()=>{window.scrollTo({top:0,behavior:"smooth"})},initial:{opacity:0,scale:0},animate:{opacity:+!!e,scale:+!!e},transition:{duration:.3},whileHover:{scale:1.1},whileTap:{scale:.9},children:(0,r.jsx)("svg",{className:"w-6 h-6 text-foreground",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 10l7-7m0 0l7 7m-7-7v18"})})})}function k(){return(0,i.useEffect)(()=>{a.e(96).then(a.bind(a,7063)).then(e=>{let{getCLS:t,getFID:a,getFCP:r,getLCP:i,getTTFB:s}=e;t(console.log),a(console.log),r(console.log),i(console.log),s(console.log)}).catch(()=>{console.log("Web Vitals not available")})},[]),null}function C(){return(0,i.useEffect)(()=>{if("PerformanceObserver"in window)try{let e=new PerformanceObserver(e=>{for(let t of e.getEntries())t.duration>50&&console.warn("Long task detected:",t)});e.observe({entryTypes:["longtask"]});let t=new PerformanceObserver(e=>{for(let t of e.getEntries())t.hadRecentInput||console.log("Layout shift:",t)});return t.observe({entryTypes:["layout-shift"]}),()=>{e.disconnect(),t.disconnect()}}catch(e){console.log("Performance monitoring not supported")}},[]),null}function P(){return(0,i.useEffect)(()=>{["https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap","https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@100;200;300;400;500;600;700;800&display=swap"].forEach(e=>{let t=document.createElement("link");t.rel="preload",t.as="style",t.href=e,document.head.appendChild(t)}),[].forEach(e=>{let t=document.createElement("link");t.rel="preload",t.as="image",t.href=e,document.head.appendChild(t)})},[]),null}function A(){return(0,i.useEffect)(()=>{"serviceWorker"in navigator&&navigator.serviceWorker.register("/sw.js").then(e=>{console.log("SW registered: ",e)}).catch(e=>{console.log("SW registration failed: ",e)})},[]),null}function B(e){let{children:t}=e;return(0,r.jsxs)("div",{className:"relative min-h-screen",children:[(0,r.jsx)(k,{}),(0,r.jsx)(C,{}),(0,r.jsx)(P,{}),(0,r.jsx)(A,{}),(0,r.jsx)(N,{}),(0,r.jsx)(v,{}),(0,r.jsx)("main",{className:"relative",children:t}),(0,r.jsx)(w,{}),(0,r.jsx)(S,{})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[96,358],()=>t(4226)),_N_E=e.O()}]);