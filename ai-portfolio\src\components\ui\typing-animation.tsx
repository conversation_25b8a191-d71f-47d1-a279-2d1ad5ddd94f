"use client"

import { useState, useEffect } from 'react'
import { cn } from '@/lib/utils'

interface TypingAnimationProps {
  text: string | string[]
  className?: string
  speed?: number
  delay?: number
  repeat?: boolean
  cursor?: boolean
  onComplete?: () => void
}

export function TypingAnimation({
  text,
  className,
  speed = 100,
  delay = 0,
  repeat = false,
  cursor = true,
  onComplete
}: TypingAnimationProps) {
  const [displayText, setDisplayText] = useState('')
  const [currentIndex, setCurrentIndex] = useState(0)
  const [currentTextIndex, setCurrentTextIndex] = useState(0)
  const [isDeleting, setIsDeleting] = useState(false)
  const [showCursor, setShowCursor] = useState(true)

  const textArray = Array.isArray(text) ? text : [text]
  const currentText = textArray[currentTextIndex]

  useEffect(() => {
    const timeout = setTimeout(() => {
      if (!isDeleting) {
        // Typing
        if (currentIndex < currentText.length) {
          setDisplayText(currentText.slice(0, currentIndex + 1))
          setCurrentIndex(currentIndex + 1)
        } else {
          // Finished typing current text
          if (textArray.length > 1) {
            // Multiple texts - start deleting after pause
            setTimeout(() => setIsDeleting(true), 2000)
          } else {
            // Single text - call onComplete
            onComplete?.()
          }
        }
      } else {
        // Deleting
        if (currentIndex > 0) {
          setDisplayText(currentText.slice(0, currentIndex - 1))
          setCurrentIndex(currentIndex - 1)
        } else {
          // Finished deleting
          setIsDeleting(false)
          setCurrentTextIndex((prev) => (prev + 1) % textArray.length)
        }
      }
    }, delay > 0 ? delay : isDeleting ? speed / 2 : speed)

    // Clear delay after first run
    if (delay > 0) {
      delay = 0
    }

    return () => clearTimeout(timeout)
  }, [currentIndex, currentText, isDeleting, speed, delay, textArray, currentTextIndex, onComplete])

  // Cursor blinking effect
  useEffect(() => {
    if (!cursor) return

    const cursorInterval = setInterval(() => {
      setShowCursor(prev => !prev)
    }, 530)

    return () => clearInterval(cursorInterval)
  }, [cursor])

  return (
    <span className={cn("inline-block", className)}>
      {displayText}
      {cursor && (
        <span 
          className={cn(
            "inline-block w-0.5 h-[1em] bg-current ml-1 transition-opacity duration-100",
            showCursor ? "opacity-100" : "opacity-0"
          )}
        />
      )}
    </span>
  )
}

// Gradient typing animation with multiple colors
export function GradientTypingAnimation({
  text,
  className,
  speed = 100,
  delay = 0,
  gradientColors = ['#00d4ff', '#8b5cf6', '#00ff88'],
  ...props
}: TypingAnimationProps & { gradientColors?: string[] }) {
  return (
    <TypingAnimation
      text={text}
      className={cn("gradient-text", className)}
      speed={speed}
      delay={delay}
      {...props}
    />
  )
}

// Matrix-style typing effect
export function MatrixTypingAnimation({
  text,
  className,
  speed = 50,
  ...props
}: TypingAnimationProps) {
  const [scrambledText, setScrambledText] = useState('')
  const [revealedChars, setRevealedChars] = useState(0)

  const textString = Array.isArray(text) ? text[0] : text
  const chars = '!@#$%^&*()_+-=[]{}|;:,.<>?'

  useEffect(() => {
    const interval = setInterval(() => {
      if (revealedChars < textString.length) {
        let newText = textString.slice(0, revealedChars)
        
        // Add scrambled characters for unrevealed part
        for (let i = revealedChars; i < textString.length; i++) {
          if (textString[i] === ' ') {
            newText += ' '
          } else {
            newText += chars[Math.floor(Math.random() * chars.length)]
          }
        }
        
        setScrambledText(newText)
        
        // Randomly reveal next character
        if (Math.random() > 0.7) {
          setRevealedChars(prev => prev + 1)
        }
      } else {
        setScrambledText(textString)
        clearInterval(interval)
      }
    }, speed)

    return () => clearInterval(interval)
  }, [textString, revealedChars, speed, chars])

  return (
    <span className={cn("font-mono text-matrix-green", className)}>
      {scrambledText}
    </span>
  )
}
